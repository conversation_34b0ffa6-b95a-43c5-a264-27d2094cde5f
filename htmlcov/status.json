{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "ff0798e5e7a8164ea5739b533177ca87", "files": {"z_18df11211f1f7c80___init___py": {"hash": "2b7d65d2621d46bc40ce4c851552a674", "index": {"url": "z_18df11211f1f7c80___init___py.html", "file": "src/chat_app/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_chat_handler_py": {"hash": "8cc840f81b574d0d123eabeebab5340f", "index": {"url": "z_18df11211f1f7c80_chat_handler_py.html", "file": "src/chat_app/chat_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_models_py": {"hash": "db43d533b1a6ec27503475008b506196", "index": {"url": "z_18df11211f1f7c80_models_py.html", "file": "src/chat_app/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_ui_components_py": {"hash": "e549a0c38be6c2a746a33525d0a0dded", "index": {"url": "z_18df11211f1f7c80_ui_components_py.html", "file": "src/chat_app/ui_components.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 76, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_01498f1c63589965___init___py": {"hash": "047caf80360e1c7d1d26e3dbf2f2c640", "index": {"url": "z_01498f1c63589965___init___py.html", "file": "src/chat_app/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_01498f1c63589965_config_py": {"hash": "9cdea8046d30acb54194d626a5273717", "index": {"url": "z_01498f1c63589965_config_py.html", "file": "src/chat_app/utils/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_01498f1c63589965_validators_py": {"hash": "c4fee90d729cbf839e9dbd00a7c7b249", "index": {"url": "z_01498f1c63589965_validators_py.html", "file": "src/chat_app/utils/validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}