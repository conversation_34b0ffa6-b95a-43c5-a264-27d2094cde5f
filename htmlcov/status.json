{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "59c557caeaec33dd72dde52ddff827bf", "files": {"z_18df11211f1f7c80___init___py": {"hash": "2b7d65d2621d46bc40ce4c851552a674", "index": {"url": "z_18df11211f1f7c80___init___py.html", "file": "src/chat_app/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_chat_handler_py": {"hash": "d95a612b5479a4a4d5ca774fc7e967c0", "index": {"url": "z_18df11211f1f7c80_chat_handler_py.html", "file": "src/chat_app/chat_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_models_py": {"hash": "1c2b073acae9a3b23a982306217037b7", "index": {"url": "z_18df11211f1f7c80_models_py.html", "file": "src/chat_app/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 64, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_ui_components_py": {"hash": "5633f8b8773ef9d7a530ab387f6ee649", "index": {"url": "z_18df11211f1f7c80_ui_components_py.html", "file": "src/chat_app/ui_components.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_01498f1c63589965___init___py": {"hash": "047caf80360e1c7d1d26e3dbf2f2c640", "index": {"url": "z_01498f1c63589965___init___py.html", "file": "src/chat_app/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_01498f1c63589965_config_py": {"hash": "baba620fe34bc8fc48eaa06666f68cba", "index": {"url": "z_01498f1c63589965_config_py.html", "file": "src/chat_app/utils/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_01498f1c63589965_validators_py": {"hash": "405ba0bddbeab5180ecb5469796e67f0", "index": {"url": "z_01498f1c63589965_validators_py.html", "file": "src/chat_app/utils/validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_knowledge_base_py": {"hash": "917cb876381804670faee734c9c056d3", "index": {"url": "z_18df11211f1f7c80_knowledge_base_py.html", "file": "src/chat_app/knowledge_base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18df11211f1f7c80_web_content_py": {"hash": "6f23f660d7b1fe1ac743d7ac17a3f259", "index": {"url": "z_18df11211f1f7c80_web_content_py.html", "file": "src/chat_app/web_content.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}