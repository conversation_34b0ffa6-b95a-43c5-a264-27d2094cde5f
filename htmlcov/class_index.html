<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">6%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 19:03 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80___init___py.html">src/chat_app/__init__.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t17">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t17"><data value='ChatMessage'>ChatMessage</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t33">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t33"><data value='ChatSession'>ChatSession</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t86">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t86"><data value='ChatHandler'>ChatHandler</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="5 24">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t16">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t16"><data value='ModelInfo'>ModelInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t25">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t25"><data value='OllamaManager'>OllamaManager</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="5 20">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965___init___py.html">src/chat_app/utils/__init__.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t10">src/chat_app/utils/config.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t10"><data value='AppConfig'>AppConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t30">src/chat_app/utils/config.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t30"><data value='Config'>AppConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html">src/chat_app/utils/config.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="3 21">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html">src/chat_app/utils/validators.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>257</td>
                <td>241</td>
                <td>0</td>
                <td class="right" data-ratio="16 257">6%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 19:03 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
