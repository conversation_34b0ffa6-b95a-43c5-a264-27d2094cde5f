<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">61%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 20:24 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80___init___py.html">src/chat_app/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html">src/chat_app/chat_handler.py</a></td>
                <td>100</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="85 100">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_knowledge_base_py.html">src/chat_app/knowledge_base.py</a></td>
                <td>127</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="104 127">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html">src/chat_app/models.py</a></td>
                <td>64</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="48 64">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html">src/chat_app/ui_components.py</a></td>
                <td>156</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="0 156">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965___init___py.html">src/chat_app/utils/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html">src/chat_app/utils/config.py</a></td>
                <td>25</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="24 25">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html">src/chat_app/utils/validators.py</a></td>
                <td>22</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="21 22">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_web_content_py.html">src/chat_app/web_content.py</a></td>
                <td>150</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="112 150">75%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>647</td>
                <td>250</td>
                <td>0</td>
                <td class="right" data-ratio="397 647">61%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 20:24 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_18df11211f1f7c80_web_content_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_18df11211f1f7c80___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
