<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">61%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 19:13 +0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80___init___py.html">src/chat_app/__init__.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t20">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t20"><data value='init__'>ChatMessage.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t25">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t25"><data value='to_dict'>ChatMessage.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t36">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t36"><data value='init__'>ChatSession.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t41">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t41"><data value='add_message'>ChatSession.add_message</data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t73">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t73"><data value='get_messages_for_api'>ChatSession.get_messages_for_api</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t77">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t77"><data value='clear_history'>ChatSession.clear_history</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t81">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t81"><data value='get_message_count'>ChatSession.get_message_count</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t89">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t89"><data value='init__'>ChatHandler.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t92">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t92"><data value='start_new_session'>ChatHandler.start_new_session</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t114">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t114"><data value='send_message'>ChatHandler.send_message</data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t157">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t157"><data value='get_current_session'>ChatHandler.get_current_session</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t161">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html#t161"><data value='clear_session'>ChatHandler.clear_session</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html">src/chat_app/chat_handler.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_chat_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t28">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t28"><data value='init__'>OllamaManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t32">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t32"><data value='get_available_models'>OllamaManager.get_available_models</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t54">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t54"><data value='validate_model_availability'>OllamaManager.validate_model_availability</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t70">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t70"><data value='get_model_info'>OllamaManager.get_model_info</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t100">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t100"><data value='format_size'>OllamaManager._format_size</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t116">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html#t116"><data value='generate_response'>OllamaManager.generate_response</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html">src/chat_app/models.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t14">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t14"><data value='render_sidebar'>render_sidebar</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t75">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t75"><data value='render_model_info'>render_model_info</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t97">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t97"><data value='render_chat_messages'>render_chat_messages</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t118">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t118"><data value='render_chat_input'>render_chat_input</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t134">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t134"><data value='render_session_stats'>render_session_stats</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t158">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t158"><data value='show_error_message'>show_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t168">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t168"><data value='show_success_message'>show_success_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t178">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t178"><data value='show_warning_message'>show_warning_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t188">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html#t188"><data value='render_loading_spinner'>render_loading_spinner</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html">src/chat_app/ui_components.py</a></td>
                <td class="name left"><a href="z_18df11211f1f7c80_ui_components_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965___init___py.html">src/chat_app/utils/__init__.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t40">src/chat_app/utils/config.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t40"><data value='get_available_models'>get_available_models</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t58">src/chat_app/utils/config.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_config_py.html#t58"><data value='validate_model'>validate_model</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_config_py.html">src/chat_app/utils/config.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html#t9">src/chat_app/utils/validators.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html#t9"><data value='validate_message'>validate_message</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html#t40">src/chat_app/utils/validators.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html#t40"><data value='sanitize_message'>sanitize_message</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html#t59">src/chat_app/utils/validators.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html#t59"><data value='validate_model_name'>validate_model_name</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html">src/chat_app/utils/validators.py</a></td>
                <td class="name left"><a href="z_01498f1c63589965_validators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>268</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="163 268">61%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 19:13 +0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
