"""
Tests for the chat_handler module.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat_app.chat_handler import ChatMessage, ChatSession, ChatHandler


class TestChatMessage:
    """Test cases for ChatMessage class."""
    
    def test_chat_message_creation(self):
        """Test ChatMessage object creation."""
        message = ChatMessage("user", "Hello, world!")
        
        assert message.role == "user"
        assert message.content == "Hello, world!"
        assert isinstance(message.timestamp, datetime)
    
    def test_chat_message_to_dict(self):
        """Test ChatMessage to_dict method."""
        message = ChatMessage("assistant", "Hello back!")
        result = message.to_dict()
        
        expected = {
            "role": "assistant",
            "content": "Hello back!"
        }
        
        assert result == expected
    
    def test_chat_message_custom_timestamp(self):
        """Test ChatMessage with custom timestamp."""
        custom_time = datetime(2023, 1, 1, 12, 0, 0)
        message = ChatMessage("user", "Test", custom_time)
        
        assert message.timestamp == custom_time


class TestChatSession:
    """Test cases for ChatSession class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.session = ChatSession("test:model")
    
    def test_chat_session_creation(self):
        """Test ChatSession object creation."""
        assert self.session.model_name == "test:model"
        assert len(self.session.messages) == 0
        assert isinstance(self.session.created_at, datetime)
    
    def test_add_message_user(self):
        """Test adding a user message."""
        result = self.session.add_message("user", "Hello!")
        
        assert result is True
        assert len(self.session.messages) == 1
        assert self.session.messages[0].role == "user"
        assert self.session.messages[0].content == "Hello!"
    
    def test_add_message_assistant(self):
        """Test adding an assistant message."""
        result = self.session.add_message("assistant", "Hi there!")
        
        assert result is True
        assert len(self.session.messages) == 1
        assert self.session.messages[0].role == "assistant"
        assert self.session.messages[0].content == "Hi there!"
    
    def test_add_message_invalid_user_message(self):
        """Test adding invalid user message."""
        # Empty message
        result = self.session.add_message("user", "")
        assert result is False
        
        # Whitespace only
        result = self.session.add_message("user", "   ")
        assert result is False
    
    @patch('chat_app.chat_handler.config.max_message_length', 10)
    def test_add_message_too_long(self):
        """Test adding message that's too long."""
        long_message = "a" * 15  # Longer than max_message_length
        result = self.session.add_message("user", long_message)
        
        assert result is False
        assert len(self.session.messages) == 0
    
    @patch('chat_app.chat_handler.config.max_messages', 2)
    def test_message_limit(self):
        """Test message history limit."""
        # Add messages up to limit
        self.session.add_message("user", "Message 1")
        self.session.add_message("assistant", "Response 1")
        self.session.add_message("user", "Message 2")
        
        # Should only keep the last 2 messages
        assert len(self.session.messages) == 2
        assert self.session.messages[0].content == "Response 1"
        assert self.session.messages[1].content == "Message 2"
    
    def test_get_messages_for_api(self):
        """Test getting messages in API format."""
        self.session.add_message("user", "Hello")
        self.session.add_message("assistant", "Hi")
        
        api_messages = self.session.get_messages_for_api()
        
        expected = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi"}
        ]
        
        assert api_messages == expected
    
    def test_clear_history(self):
        """Test clearing message history."""
        self.session.add_message("user", "Hello")
        self.session.add_message("assistant", "Hi")
        
        assert len(self.session.messages) == 2
        
        self.session.clear_history()
        
        assert len(self.session.messages) == 0
    
    def test_get_message_count(self):
        """Test getting message count."""
        assert self.session.get_message_count() == 0
        
        self.session.add_message("user", "Hello")
        assert self.session.get_message_count() == 1
        
        self.session.add_message("assistant", "Hi")
        assert self.session.get_message_count() == 2


class TestChatHandler:
    """Test cases for ChatHandler class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.handler = ChatHandler()
    
    @patch('chat_app.chat_handler.ollama_manager.validate_model_availability')
    def test_start_new_session_success(self, mock_validate):
        """Test starting a new session successfully."""
        mock_validate.return_value = True
        
        result = self.handler.start_new_session("test:model")
        
        assert result is True
        assert self.handler.current_session is not None
        assert self.handler.current_session.model_name == "test:model"
    
    @patch('chat_app.chat_handler.ollama_manager.validate_model_availability')
    def test_start_new_session_invalid_model(self, mock_validate):
        """Test starting session with invalid model."""
        mock_validate.return_value = False
        
        result = self.handler.start_new_session("invalid:model")
        
        assert result is False
        assert self.handler.current_session is None
    
    def test_get_current_session_none(self):
        """Test getting current session when none exists."""
        assert self.handler.get_current_session() is None
    
    @patch('chat_app.chat_handler.ollama_manager.validate_model_availability')
    def test_get_current_session_exists(self, mock_validate):
        """Test getting current session when one exists."""
        mock_validate.return_value = True
        self.handler.start_new_session("test:model")
        
        session = self.handler.get_current_session()
        
        assert session is not None
        assert session.model_name == "test:model"
    
    def test_send_message_no_session(self):
        """Test sending message without active session."""
        result = self.handler.send_message("Hello")
        
        assert result is None
    
    @patch('chat_app.chat_handler.ollama_manager.validate_model_availability')
    @patch('chat_app.chat_handler.ollama_manager.generate_response')
    def test_send_message_success(self, mock_generate, mock_validate):
        """Test sending message successfully."""
        mock_validate.return_value = True
        mock_generate.return_value = iter(["Hello", " back", "!"])
        
        # Start session
        self.handler.start_new_session("test:model")
        
        # Send message
        result = self.handler.send_message("Hello")
        
        assert result == "Hello back!"
        
        # Check session has both messages
        session = self.handler.get_current_session()
        assert len(session.messages) == 2
        assert session.messages[0].role == "user"
        assert session.messages[0].content == "Hello"
        assert session.messages[1].role == "assistant"
        assert session.messages[1].content == "Hello back!"
    
    @patch('chat_app.chat_handler.ollama_manager.validate_model_availability')
    def test_clear_session(self, mock_validate):
        """Test clearing session."""
        mock_validate.return_value = True
        
        # Start session and add messages
        self.handler.start_new_session("test:model")
        self.handler.current_session.add_message("user", "Hello")
        
        assert len(self.handler.current_session.messages) == 1
        
        # Clear session
        self.handler.clear_session()
        
        assert len(self.handler.current_session.messages) == 0
