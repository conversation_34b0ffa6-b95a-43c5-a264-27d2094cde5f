"""
Tests for the models module.
"""

import pytest
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat_app.models import OllamaManager, ModelInfo


class TestOllamaManager:
    """Test cases for OllamaManager class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.manager = OllamaManager()
    
    @patch('chat_app.models.ollama.Client')
    def test_get_available_models_success(self, mock_client):
        """Test successful retrieval of available models."""
        # Mock the client response with structured objects
        mock_instance = Mock()
        mock_model1 = Mock()
        mock_model1.model = 'llama3.2:latest'
        mock_model2 = Mock()
        mock_model2.model = 'gemma:2b'

        mock_response = Mock()
        mock_response.models = [mock_model1, mock_model2]
        mock_instance.list.return_value = mock_response
        mock_client.return_value = mock_instance

        # Create new manager to trigger the mocked client
        manager = OllamaManager()
        models = manager.get_available_models()

        assert 'llama3.2:latest' in models
        assert 'gemma:2b' in models
    
    @patch('chat_app.models.ollama.Client')
    def test_get_available_models_fallback(self, mock_client):
        """Test fallback to predefined models when API fails."""
        # Mock the client to raise an exception
        mock_instance = Mock()
        mock_instance.list.side_effect = Exception("Connection failed")
        mock_client.return_value = mock_instance
        
        # Create new manager to trigger the mocked client
        manager = OllamaManager()
        models = manager.get_available_models()
        
        # Should fallback to predefined models
        assert len(models) > 0
        assert 'llama3.2:latest' in models
    
    def test_validate_model_availability_valid(self):
        """Test model validation with valid model."""
        # Mock the available models
        self.manager._available_models = ['llama3.2:latest', 'gemma:2b']
        
        assert self.manager.validate_model_availability('llama3.2:latest') is True
    
    def test_validate_model_availability_invalid(self):
        """Test model validation with invalid model."""
        # Mock the available models
        self.manager._available_models = ['llama3.2:latest', 'gemma:2b']
        
        assert self.manager.validate_model_availability('nonexistent:model') is False
    
    def test_validate_model_availability_invalid_name(self):
        """Test model validation with invalid model name format."""
        assert self.manager.validate_model_availability('') is False
        assert self.manager.validate_model_availability('invalid name!') is False
    
    @patch('chat_app.models.ollama.Client')
    def test_get_model_info_success(self, mock_client):
        """Test successful retrieval of model info."""
        # Mock the client response with structured objects
        mock_instance = Mock()
        mock_model = Mock()
        mock_model.model = 'llama3.2:latest'
        mock_model.size = 2000000000  # 2GB in bytes
        mock_model.modified_at = Mock()
        mock_model.modified_at.isoformat.return_value = '2023-01-01T00:00:00'
        mock_model.details = Mock()
        mock_model.details.family = 'llama'
        mock_model.details.parameter_size = '3B'

        mock_response = Mock()
        mock_response.models = [mock_model]
        mock_instance.list.return_value = mock_response
        mock_client.return_value = mock_instance

        # Create new manager to trigger the mocked client
        manager = OllamaManager()
        model_info = manager.get_model_info('llama3.2:latest')

        assert model_info is not None
        assert model_info.name == 'llama3.2:latest'
        assert model_info.size == '1.9 GB'  # Formatted size
        assert model_info.family == 'llama'
        assert model_info.parameter_size == '3B'
    
    @patch('chat_app.models.ollama.Client')
    def test_get_model_info_not_found(self, mock_client):
        """Test model info retrieval for non-existent model."""
        # Mock the client response with empty models list
        mock_instance = Mock()
        mock_response = Mock()
        mock_response.models = []
        mock_instance.list.return_value = mock_response
        mock_client.return_value = mock_instance

        # Create new manager to trigger the mocked client
        manager = OllamaManager()
        model_info = manager.get_model_info('nonexistent:model')

        assert model_info is None


class TestModelInfo:
    """Test cases for ModelInfo class."""
    
    def test_model_info_creation(self):
        """Test ModelInfo object creation."""
        model_info = ModelInfo(
            name="test:model",
            size="1GB",
            family="test_family"
        )
        
        assert model_info.name == "test:model"
        assert model_info.size == "1GB"
        assert model_info.family == "test_family"
        assert model_info.modified is None
        assert model_info.parameter_size is None
    
    def test_model_info_minimal(self):
        """Test ModelInfo with minimal required fields."""
        model_info = ModelInfo(name="minimal:model")
        
        assert model_info.name == "minimal:model"
        assert model_info.size is None
        assert model_info.family is None
