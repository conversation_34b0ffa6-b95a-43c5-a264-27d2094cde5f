"""
Tests for the knowledge_base module.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path
import tempfile
import shutil

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat_app.knowledge_base import KnowledgeBase


class TestKnowledgeBase:
    """Test cases for KnowledgeBase class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Use a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        
    def teardown_method(self):
        """Clean up test fixtures."""
        # Clean up temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_initialize_db_success(self, mock_client):
        """Test successful database initialization."""
        # Mock ChromaDB client and collection
        mock_collection = Mock()
        mock_collection.count.return_value = 0
        
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        assert kb.client is not None
        assert kb.collection is not None
        mock_client.assert_called_once()
    
    def test_generate_document_id(self):
        """Test document ID generation."""
        with patch('chat_app.knowledge_base.chromadb.PersistentClient'):
            kb = KnowledgeBase(persist_directory=self.temp_dir)
            
            doc_id = kb._generate_document_id("https://example.com", "test content")
            
            assert isinstance(doc_id, str)
            assert len(doc_id) > 0
            assert "_" in doc_id
    
    def test_chunk_content_small(self):
        """Test content chunking for small content."""
        with patch('chat_app.knowledge_base.chromadb.PersistentClient'):
            kb = KnowledgeBase(persist_directory=self.temp_dir)
            
            content = "This is a small piece of content."
            chunks = kb._chunk_content(content, chunk_size=100)
            
            assert len(chunks) == 1
            assert chunks[0] == content
    
    def test_chunk_content_large(self):
        """Test content chunking for large content."""
        with patch('chat_app.knowledge_base.chromadb.PersistentClient'):
            kb = KnowledgeBase(persist_directory=self.temp_dir)
            
            # Create content larger than chunk size
            content = "This is a sentence. " * 100  # Much larger than default chunk size
            chunks = kb._chunk_content(content, chunk_size=100, overlap=10)
            
            assert len(chunks) > 1
            # Check that chunks have some overlap
            assert len(chunks[0]) <= 100 + 50  # Allow some flexibility for sentence boundaries
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_add_document_success(self, mock_client):
        """Test successful document addition."""
        # Mock ChromaDB collection
        mock_collection = Mock()
        mock_collection.get.return_value = {'ids': []}  # No existing documents
        mock_collection.add.return_value = None
        
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        result = kb.add_document(
            title="Test Document",
            content="This is test content for the document.",
            url="https://example.com",
            content_type="html"
        )
        
        assert result is True
        mock_collection.add.assert_called_once()
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_add_document_empty_content(self, mock_client):
        """Test adding document with empty content."""
        mock_collection = Mock()
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        result = kb.add_document(
            title="Test Document",
            content="",
            url="https://example.com",
            content_type="html"
        )
        
        assert result is False
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_search_content_success(self, mock_client):
        """Test successful content search."""
        # Mock search results
        mock_results = {
            'ids': [['doc1', 'doc2']],
            'documents': [['Content 1', 'Content 2']],
            'metadatas': [[
                {'title': 'Doc 1', 'url': 'https://example.com/1'},
                {'title': 'Doc 2', 'url': 'https://example.com/2'}
            ]],
            'distances': [[0.1, 0.2]]
        }
        
        mock_collection = Mock()
        mock_collection.query.return_value = mock_results
        mock_collection.count.return_value = 10
        
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        results = kb.search_content("test query", n_results=2)
        
        assert len(results) == 2
        assert results[0]['content'] == 'Content 1'
        assert results[0]['metadata']['title'] == 'Doc 1'
        assert results[0]['distance'] == 0.1
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_search_content_empty_query(self, mock_client):
        """Test search with empty query."""
        mock_collection = Mock()
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        results = kb.search_content("", n_results=5)
        
        assert len(results) == 0
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_get_all_documents(self, mock_client):
        """Test getting all documents."""
        mock_results = {
            'ids': ['doc1_0', 'doc1_1', 'doc2_0'],
            'metadatas': [
                {
                    'url': 'https://example.com/1',
                    'title': 'Doc 1',
                    'content_type': 'html',
                    'added_at': '2023-01-01T00:00:00',
                    'total_chunks': 2
                },
                {
                    'url': 'https://example.com/1',
                    'title': 'Doc 1',
                    'content_type': 'html',
                    'added_at': '2023-01-01T00:00:00',
                    'total_chunks': 2
                },
                {
                    'url': 'https://example.com/2',
                    'title': 'Doc 2',
                    'content_type': 'pdf',
                    'added_at': '2023-01-02T00:00:00',
                    'total_chunks': 1
                }
            ]
        }
        
        mock_collection = Mock()
        mock_collection.get.return_value = mock_results
        
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        documents = kb.get_all_documents()
        
        # Should group by URL, so only 2 unique documents
        assert len(documents) == 2
        assert documents[0]['url'] == 'https://example.com/1'
        assert documents[1]['url'] == 'https://example.com/2'
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_delete_document(self, mock_client):
        """Test document deletion."""
        mock_collection = Mock()
        mock_collection.delete.return_value = None
        
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        result = kb.delete_document("https://example.com")
        
        assert result is True
        mock_collection.delete.assert_called_once_with(where={"url": "https://example.com"})
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_clear_all(self, mock_client):
        """Test clearing all documents."""
        mock_collection = Mock()
        mock_client_instance = Mock()
        mock_client_instance.delete_collection.return_value = None
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        result = kb.clear_all()
        
        assert result is True
        mock_client_instance.delete_collection.assert_called_once_with("web_content")
    
    @patch('chat_app.knowledge_base.chromadb.PersistentClient')
    def test_get_stats(self, mock_client):
        """Test getting knowledge base statistics."""
        mock_collection = Mock()
        mock_collection.count.return_value = 5
        
        mock_client_instance = Mock()
        mock_client_instance.get_or_create_collection.return_value = mock_collection
        mock_client.return_value = mock_client_instance
        
        kb = KnowledgeBase(persist_directory=self.temp_dir)
        
        # Mock get_all_documents
        with patch.object(kb, 'get_all_documents') as mock_get_all:
            mock_get_all.return_value = [
                {'content_type': 'html'},
                {'content_type': 'html'},
                {'content_type': 'pdf'}
            ]
            
            stats = kb.get_stats()
        
        assert stats['total_documents'] == 3
        assert stats['total_chunks'] == 5
        assert stats['content_types']['html'] == 2
        assert stats['content_types']['pdf'] == 1
        assert stats['avg_chunks_per_doc'] == 5/3
