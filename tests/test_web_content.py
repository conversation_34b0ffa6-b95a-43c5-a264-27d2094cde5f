"""
Tests for the web_content module.
"""

import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat_app.web_content import WebContentDownloader, ContentType


class TestWebContentDownloader:
    """Test cases for WebContentDownloader class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.downloader = WebContentDownloader()
    
    def test_validate_urls_valid(self):
        """Test URL validation with valid URLs."""
        urls = [
            "https://example.com",
            "http://test.org/page",
            "example.com/article",  # Should add https://
            "www.site.net"  # Should add https://
        ]
        
        valid, invalid = self.downloader.validate_urls(urls)
        
        assert len(valid) == 4
        assert len(invalid) == 0
        assert "https://example.com" in valid
        assert "https://example.com/article" in valid
    
    def test_validate_urls_invalid(self):
        """Test URL validation with invalid URLs."""
        urls = [
            "not-a-url",
            "",
            "   ",
            "ftp://invalid.com"
        ]
        
        valid, invalid = self.downloader.validate_urls(urls)
        
        assert len(valid) == 0
        assert len(invalid) > 0
    
    def test_detect_content_type_pdf_extension(self):
        """Test content type detection for PDF by extension."""
        url = "https://example.com/document.pdf"
        mock_response = Mock()
        mock_response.headers = {}
        
        content_type = self.downloader.detect_content_type(url, mock_response)
        
        assert content_type == ContentType.PDF
    
    def test_detect_content_type_pdf_header(self):
        """Test content type detection for PDF by header."""
        url = "https://example.com/document"
        mock_response = Mock()
        mock_response.headers = {"content-type": "application/pdf"}
        
        content_type = self.downloader.detect_content_type(url, mock_response)
        
        assert content_type == ContentType.PDF
    
    def test_detect_content_type_html(self):
        """Test content type detection for HTML."""
        url = "https://example.com/page"
        mock_response = Mock()
        mock_response.headers = {"content-type": "text/html; charset=utf-8"}
        
        content_type = self.downloader.detect_content_type(url, mock_response)
        
        assert content_type == ContentType.HTML
    
    @patch('chat_app.web_content.requests.Session.get')
    def test_download_content_success(self, mock_get):
        """Test successful content download."""
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "text/html"}
        mock_response.content = b"<html><body>Test content</body></html>"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.downloader.download_content("https://example.com")
        
        assert result is not None
        assert result['url'] == "https://example.com"
        assert result['content_type'] == ContentType.HTML
        assert result['status_code'] == 200
    
    @patch('chat_app.web_content.requests.Session.get')
    def test_download_content_failure(self, mock_get):
        """Test failed content download."""
        mock_get.side_effect = Exception("Network error")
        
        result = self.downloader.download_content("https://example.com")
        
        assert result is None
    
    def test_parse_html_content_success(self):
        """Test successful HTML parsing."""
        html_content = b"""
        <html>
            <head><title>Test Page</title></head>
            <body>
                <h1>Main Title</h1>
                <p>This is a test paragraph.</p>
                <script>alert('test');</script>
            </body>
        </html>
        """
        
        result = self.downloader.parse_html_content(html_content, "https://example.com")
        
        assert result is not None
        assert result['title'] == "Test Page"
        assert result['url'] == "https://example.com"
        assert result['content_type'] == ContentType.HTML
        assert "test paragraph" in result['content']
        assert "alert" not in result['content']  # Script should be removed
        # Note: HTML converter may filter headers, so we check for paragraph content
    
    def test_parse_html_content_no_title(self):
        """Test HTML parsing without title."""
        html_content = b"<html><body><p>Content without title</p></body></html>"
        
        result = self.downloader.parse_html_content(html_content, "https://example.com")
        
        assert result is not None
        assert result['title'] == "No Title"
    
    @patch('chat_app.web_content.tempfile.NamedTemporaryFile')
    @patch('chat_app.web_content.pypdf.PdfReader')
    def test_parse_pdf_content_success(self, mock_pdf_reader, mock_temp_file):
        """Test successful PDF parsing."""
        # Mock temporary file
        mock_temp_file.return_value.__enter__.return_value.name = "/tmp/test.pdf"
        
        # Mock PDF reader
        mock_page = Mock()
        mock_page.extract_text.return_value = "This is page content"
        
        mock_reader_instance = Mock()
        mock_reader_instance.pages = [mock_page]
        mock_reader_instance.metadata = {'/Title': 'Test PDF'}
        
        mock_pdf_reader.return_value = mock_reader_instance
        
        # Mock file operations
        with patch('builtins.open', create=True), \
             patch('chat_app.web_content.os.unlink'):
            
            result = self.downloader.parse_pdf_content(b"fake pdf content", "https://example.com/doc.pdf")
        
        assert result is not None
        assert result['title'] == 'Test PDF'
        assert result['url'] == "https://example.com/doc.pdf"
        assert result['content_type'] == ContentType.PDF
        assert "This is page content" in result['content']
    
    @patch('chat_app.web_content.WebContentDownloader.download_content')
    @patch('chat_app.web_content.WebContentDownloader.parse_html_content')
    def test_process_urls_success(self, mock_parse_html, mock_download):
        """Test successful URL processing."""
        # Mock download
        mock_download.return_value = {
            'url': 'https://example.com',
            'content_type': ContentType.HTML,
            'content_bytes': b'<html>test</html>',
            'headers': {},
            'status_code': 200
        }
        
        # Mock parsing
        mock_parse_html.return_value = {
            'title': 'Test Page',
            'content': 'Test content',
            'url': 'https://example.com',
            'content_type': ContentType.HTML
        }
        
        urls = ['https://example.com']
        results = self.downloader.process_urls(urls)
        
        assert len(results['successful']) == 1
        assert len(results['failed']) == 0
        assert len(results['invalid_urls']) == 0
        assert results['successful'][0]['title'] == 'Test Page'
    
    def test_process_urls_invalid(self):
        """Test processing invalid URLs."""
        urls = ['not-a-url', 'invalid']
        results = self.downloader.process_urls(urls)
        
        assert len(results['successful']) == 0
        assert len(results['failed']) == 0
        assert len(results['invalid_urls']) == 2
