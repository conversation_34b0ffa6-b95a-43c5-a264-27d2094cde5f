# API Documentation - Ollama Chat Application

## Table of Contents
- [Overview](#overview)
- [Core APIs](#core-apis)
- [Integration APIs](#integration-apis)
- [Data Models](#data-models)
- [Error Handling](#error-handling)
- [Usage Examples](#usage-examples)

## Overview

The Ollama Chat Application provides a comprehensive set of APIs for managing AI conversations, web content processing, and knowledge base operations. This documentation covers all public interfaces and integration points.

### API Architecture

```mermaid
graph TB
    subgraph "External APIs"
        OLLAMA[Ollama API]
        WEB[Web Content APIs]
    end
    
    subgraph "Application APIs"
        CHAT[Chat Handler API]
        KB[Knowledge Base API]
        WC[Web Content API]
        MODEL[Model Manager API]
    end
    
    subgraph "Data Layer APIs"
        CHROMA[ChromaDB API]
        CONFIG[Configuration API]
    end
    
    CHAT --> MODEL
    CHAT --> KB
    WC --> KB
    MODEL --> OLLAMA
    WC --> WEB
    KB --> CHROMA
    
    style CHAT fill:#e1f5fe
    style KB fill:#e8f5e8
    style WC fill:#fff3e0
    style MODEL fill:#f3e5f5
```

## Core APIs

### Chat Handler API

#### Class: `ChatHandler`

The main interface for managing chat sessions and generating AI responses.

##### `start_new_session(model_name: str) -> bool`

Initializes a new chat session with the specified AI model.

**Parameters:**
- `model_name` (str): Name of the Ollama model to use

**Returns:**
- `bool`: True if session started successfully, False otherwise

**Raises:**
- `ValueError`: If model_name is invalid or empty
- `ConnectionError`: If Ollama service is unavailable

**Example:**
```python
from chat_app.chat_handler import chat_handler

# Start a new session
success = chat_handler.start_new_session("llama3.2:latest")
if success:
    print("Session started successfully")
else:
    print("Failed to start session")
```

##### `send_message(user_message: str) -> Optional[str]`

Sends a user message and generates an AI response with knowledge base context.

**Parameters:**
- `user_message` (str): The user's input message

**Returns:**
- `Optional[str]`: AI response string or None if error occurred

**Raises:**
- `ValueError`: If message is empty or invalid
- `RuntimeError`: If no active session exists

**Example:**
```python
# Send a message
response = chat_handler.send_message("What is Python?")
if response:
    print(f"AI: {response}")
else:
    print("Failed to get response")
```

##### `get_current_session() -> Optional[ChatSession]`

Retrieves the current active chat session.

**Returns:**
- `Optional[ChatSession]`: Current session object or None

**Example:**
```python
session = chat_handler.get_current_session()
if session:
    print(f"Current model: {session.model_name}")
    print(f"Message count: {session.get_message_count()}")
```

##### `clear_session() -> None`

Clears all messages from the current session.

**Example:**
```python
chat_handler.clear_session()
print("Session cleared")
```

### Knowledge Base API

#### Class: `KnowledgeBase`

Manages document storage and semantic search using ChromaDB.

##### `add_document(title: str, content: str, url: str, content_type: str) -> bool`

Adds a document to the knowledge base with automatic chunking.

**Parameters:**
- `title` (str): Document title
- `content` (str): Full text content
- `url` (str): Source URL
- `content_type` (str): Content type ('html' or 'pdf')

**Returns:**
- `bool`: True if document added successfully

**Raises:**
- `ValueError`: If content is empty or parameters are invalid
- `StorageError`: If ChromaDB operation fails

**Example:**
```python
from chat_app.knowledge_base import knowledge_base

success = knowledge_base.add_document(
    title="Python Tutorial",
    content="Python is a programming language...",
    url="https://docs.python.org/3/tutorial/",
    content_type="html"
)
```

##### `search_content(query: str, n_results: int = 5) -> List[Dict[str, Any]]`

Searches for relevant content using semantic similarity.

**Parameters:**
- `query` (str): Search query string
- `n_results` (int, optional): Maximum number of results (default: 5)

**Returns:**
- `List[Dict[str, Any]]`: List of relevant documents with metadata

**Response Format:**
```python
[
    {
        'id': 'doc1_chunk0',
        'content': 'Relevant text content...',
        'metadata': {
            'title': 'Document Title',
            'url': 'https://example.com',
            'content_type': 'html',
            'chunk_index': 0,
            'total_chunks': 5,
            'added_at': '2023-12-01T10:00:00'
        },
        'distance': 0.15  # Lower is more similar
    }
]
```

**Example:**
```python
results = knowledge_base.search_content("Python functions", n_results=3)
for result in results:
    print(f"Title: {result['metadata']['title']}")
    print(f"Content: {result['content'][:100]}...")
    print(f"Similarity: {1 - result['distance']:.2%}")
```

##### `get_all_documents() -> List[Dict[str, Any]]`

Retrieves all documents in the knowledge base.

**Returns:**
- `List[Dict[str, Any]]`: List of document metadata

**Response Format:**
```python
[
    {
        'url': 'https://example.com',
        'title': 'Document Title',
        'content_type': 'html',
        'added_at': '2023-12-01T10:00:00',
        'chunk_count': 5
    }
]
```

##### `delete_document(url: str) -> bool`

Deletes all chunks of a document by URL.

**Parameters:**
- `url` (str): URL of the document to delete

**Returns:**
- `bool`: True if deletion successful

##### `clear_all() -> bool`

Removes all documents from the knowledge base.

**Returns:**
- `bool`: True if operation successful

##### `get_stats() -> Dict[str, Any]`

Retrieves knowledge base statistics.

**Returns:**
- `Dict[str, Any]`: Statistics dictionary

**Response Format:**
```python
{
    'total_documents': 10,
    'total_chunks': 45,
    'content_types': {'html': 7, 'pdf': 3},
    'avg_chunks_per_doc': 4.5
}
```

### Web Content API

#### Class: `WebContentDownloader`

Handles downloading and parsing of web content.

##### `validate_urls(urls: List[str]) -> Tuple[List[str], List[str]]`

Validates a list of URLs for format and security.

**Parameters:**
- `urls` (List[str]): List of URLs to validate

**Returns:**
- `Tuple[List[str], List[str]]`: (valid_urls, invalid_urls)

**Example:**
```python
from chat_app.web_content import web_downloader

urls = ["https://example.com", "invalid-url", "https://test.org"]
valid, invalid = web_downloader.validate_urls(urls)
print(f"Valid: {valid}")
print(f"Invalid: {invalid}")
```

##### `process_urls(urls: List[str]) -> Dict[str, Any]`

Downloads and processes multiple URLs.

**Parameters:**
- `urls` (List[str]): List of URLs to process

**Returns:**
- `Dict[str, Any]`: Processing results

**Response Format:**
```python
{
    'successful': [
        {
            'title': 'Page Title',
            'content': 'Extracted text content...',
            'url': 'https://example.com',
            'content_type': 'html'
        }
    ],
    'failed': [
        {
            'url': 'https://failed.com',
            'error': 'Network timeout'
        }
    ],
    'invalid_urls': ['invalid-url']
}
```

**Example:**
```python
urls = [
    "https://docs.python.org/3/tutorial/",
    "https://example.com/document.pdf"
]

results = web_downloader.process_urls(urls)
print(f"Processed {len(results['successful'])} documents")
print(f"Failed: {len(results['failed'])}")
```

### Model Manager API

#### Class: `OllamaManager`

Manages Ollama model interactions and information.

##### `get_available_models() -> List[str]`

Retrieves list of available Ollama models.

**Returns:**
- `List[str]`: List of model names

**Example:**
```python
from chat_app.models import ollama_manager

models = ollama_manager.get_available_models()
print("Available models:", models)
```

##### `validate_model_availability(model_name: str) -> bool`

Checks if a specific model is available.

**Parameters:**
- `model_name` (str): Name of the model to check

**Returns:**
- `bool`: True if model is available

##### `get_model_info(model_name: str) -> Optional[ModelInfo]`

Retrieves detailed information about a model.

**Parameters:**
- `model_name` (str): Name of the model

**Returns:**
- `Optional[ModelInfo]`: Model information object or None

**Example:**
```python
info = ollama_manager.get_model_info("llama3.2:latest")
if info:
    print(f"Model: {info.name}")
    print(f"Size: {info.size}")
    print(f"Family: {info.family}")
```

##### `generate_response(model: str, messages: List[Dict], stream: bool = True) -> Generator[str, None, None]`

Generates AI response from the model.

**Parameters:**
- `model` (str): Model name to use
- `messages` (List[Dict]): List of message dictionaries
- `stream` (bool, optional): Whether to stream response (default: True)

**Returns:**
- `Generator[str, None, None]`: Response chunks

**Message Format:**
```python
messages = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello!"},
    {"role": "assistant", "content": "Hi there!"},
    {"role": "user", "content": "How are you?"}
]
```

## Integration APIs

### Ollama API Integration

The application integrates with the Ollama API for AI model operations.

#### Base URL
```
http://localhost:11434/api
```

#### Endpoints Used

##### GET `/api/tags`
Lists available models.

**Response:**
```json
{
  "models": [
    {
      "name": "llama3.2:latest",
      "size": 2000000000,
      "digest": "sha256:...",
      "modified_at": "2023-12-01T10:00:00Z"
    }
  ]
}
```

##### POST `/api/chat`
Generates chat completions.

**Request:**
```json
{
  "model": "llama3.2:latest",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "stream": true
}
```

**Response (Streaming):**
```json
{"message": {"role": "assistant", "content": "Hello"}, "done": false}
{"message": {"role": "assistant", "content": "!"}, "done": false}
{"message": {"role": "assistant", "content": ""}, "done": true}
```

### ChromaDB API Integration

The application uses ChromaDB for vector storage and semantic search.

#### Collection Operations

##### Create Collection
```python
collection = client.create_collection(
    name="web_content",
    metadata={"description": "Web content for chat context"}
)
```

##### Add Documents
```python
collection.add(
    ids=["doc1", "doc2"],
    documents=["Content 1", "Content 2"],
    metadatas=[
        {"url": "https://example.com/1", "type": "html"},
        {"url": "https://example.com/2", "type": "pdf"}
    ]
)
```

##### Query Documents
```python
results = collection.query(
    query_texts=["search query"],
    n_results=5,
    where={"type": "html"}
)
```

## Data Models

### ChatMessage

```python
@dataclass
class ChatMessage:
    role: str  # 'user' | 'assistant' | 'system'
    content: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, str]:
        return {
            "role": self.role,
            "content": self.content
        }
```

### ChatSession

```python
@dataclass
class ChatSession:
    model_name: str
    messages: List[ChatMessage]
    created_at: datetime
    
    def add_message(self, role: str, content: str) -> bool
    def get_messages_for_api(self) -> List[Dict[str, str]]
    def clear_history(self) -> None
    def get_message_count(self) -> int
```

### ModelInfo

```python
@dataclass
class ModelInfo:
    name: str
    size: Optional[str] = None
    modified: Optional[str] = None
    family: Optional[str] = None
    parameter_size: Optional[str] = None
```

## Error Handling

### Exception Types

#### `ValidationError`
Raised when input validation fails.

```python
try:
    chat_handler.send_message("")
except ValueError as e:
    print(f"Validation error: {e}")
```

#### `ConnectionError`
Raised when external service is unavailable.

```python
try:
    chat_handler.start_new_session("llama3.2:latest")
except ConnectionError as e:
    print(f"Service unavailable: {e}")
```

#### `StorageError`
Raised when database operations fail.

```python
try:
    knowledge_base.add_document(title, content, url, type)
except StorageError as e:
    print(f"Storage error: {e}")
```

### Error Response Format

```python
{
    "error": {
        "type": "ValidationError",
        "message": "Message cannot be empty",
        "code": "INVALID_INPUT",
        "details": {
            "field": "user_message",
            "value": ""
        }
    }
}
```

## Usage Examples

### Complete Chat Flow

```python
from chat_app.chat_handler import chat_handler
from chat_app.knowledge_base import knowledge_base
from chat_app.web_content import web_downloader

# 1. Add knowledge base content
urls = ["https://docs.python.org/3/tutorial/"]
results = web_downloader.process_urls(urls)

for doc in results['successful']:
    knowledge_base.add_document(
        title=doc['title'],
        content=doc['content'],
        url=doc['url'],
        content_type=doc['content_type']
    )

# 2. Start chat session
chat_handler.start_new_session("llama3.2:latest")

# 3. Send messages
response1 = chat_handler.send_message("What is Python?")
print(f"AI: {response1}")

response2 = chat_handler.send_message("How do I define functions?")
print(f"AI: {response2}")

# 4. Get session info
session = chat_handler.get_current_session()
print(f"Messages in session: {session.get_message_count()}")
```

### Batch Document Processing

```python
# Process multiple documents
urls = [
    "https://docs.python.org/3/tutorial/",
    "https://fastapi.tiangolo.com/tutorial/",
    "https://streamlit.io/docs"
]

# Download and process
results = web_downloader.process_urls(urls)

# Add to knowledge base
for doc in results['successful']:
    success = knowledge_base.add_document(
        title=doc['title'],
        content=doc['content'],
        url=doc['url'],
        content_type=doc['content_type']
    )
    print(f"Added {doc['title']}: {success}")

# Search knowledge base
search_results = knowledge_base.search_content("web framework", n_results=5)
for result in search_results:
    print(f"Found: {result['metadata']['title']}")
    print(f"Relevance: {1 - result['distance']:.2%}")
```

### Model Management

```python
from chat_app.models import ollama_manager

# List available models
models = ollama_manager.get_available_models()
print("Available models:")
for model in models:
    info = ollama_manager.get_model_info(model)
    print(f"  {model}: {info.size if info else 'Unknown size'}")

# Validate model
if ollama_manager.validate_model_availability("llama3.2:latest"):
    print("Model is available")
else:
    print("Model not found")
```

This API documentation provides comprehensive coverage of all public interfaces in the Ollama Chat Application, enabling developers to integrate and extend the system effectively.
