# Deployment Guide - Ollama Chat Application

## Table of Contents
- [Local Development Setup](#local-development-setup)
- [Docker Deployment](#docker-deployment)
- [Production Deployment](#production-deployment)
- [Cloud Deployment](#cloud-deployment)
- [Monitoring and Maintenance](#monitoring-and-maintenance)

## Local Development Setup

### Prerequisites Installation

#### 1. Install Ollama
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# Download from https://ollama.ai/download/windows
```

#### 2. Install Python Dependencies
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

#### 3. Download AI Models
```bash
# Download recommended models
ollama pull llama3.2:latest
ollama pull gemma:2b
ollama pull qwen2.5:3b
ollama pull phi3:latest

# Verify installation
ollama list
```

### Environment Configuration

#### Create `.env` file
```bash
# Copy example configuration
cp .env.example .env

# Edit configuration
nano .env
```

#### Example Development Configuration
```env
# Development Environment
APP_TITLE="Ollama Chat (Development)"
APP_ICON="🔧"
PAGE_TITLE="Chat with AI - Dev"

# Ollama Settings
OLLAMA_HOST="http://localhost:11434"
DEFAULT_MODEL="llama3.2:latest"

# Chat Settings
MAX_MESSAGES=50
MAX_MESSAGE_LENGTH=2000

# Knowledge Base Settings
USE_KNOWLEDGE_BASE=true
CHROMA_DB_PATH="./dev_chroma_db"
MAX_SEARCH_RESULTS=3
CHUNK_SIZE=800
CHUNK_OVERLAP=80

# UI Settings
SIDEBAR_WIDTH=300
SHOW_MODEL_INFO=true
```

### Running the Application

```bash
# Start Ollama service (if not running)
ollama serve

# Run the Streamlit application
streamlit run app.py

# Access the application
# Open http://localhost:8501 in your browser
```

### Development Workflow

```mermaid
graph LR
    CODE[Code Changes] --> TEST[Run Tests]
    TEST --> LINT[Code Linting]
    LINT --> RUN[Run Locally]
    RUN --> VERIFY[Manual Testing]
    VERIFY --> COMMIT[Git Commit]
    
    TEST -.-> FIX1[Fix Test Failures]
    LINT -.-> FIX2[Fix Linting Issues]
    VERIFY -.-> FIX3[Fix Bugs]
    
    FIX1 --> CODE
    FIX2 --> CODE
    FIX3 --> CODE
```

## Docker Deployment

### Single Container Deployment

#### Dockerfile
```dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Expose port
EXPOSE 8501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Run application
CMD ["streamlit", "run", "app.py", "--server.address", "0.0.0.0"]
```

#### Build and Run
```bash
# Build the image
docker build -t ollama-chat-app .

# Run the container
docker run -d \
  --name ollama-chat \
  -p 8501:8501 \
  -e OLLAMA_HOST=http://host.docker.internal:11434 \
  -v $(pwd)/chroma_db:/app/chroma_db \
  ollama-chat-app

# View logs
docker logs -f ollama-chat
```

### Multi-Container Deployment with Docker Compose

#### docker-compose.yml
```yaml
version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-service
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    restart: unless-stopped
    
  chat-app:
    build: .
    container_name: chat-app
    ports:
      - "8501:8501"
    environment:
      - OLLAMA_HOST=http://ollama:11434
      - CHROMA_DB_PATH=/app/data/chroma_db
    volumes:
      - chat_data:/app/data
    depends_on:
      - ollama
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - chat-app
    restart: unless-stopped

volumes:
  ollama_data:
  chat_data:
```

#### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream chat_app {
        server chat-app:8501;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            proxy_pass http://chat_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support for Streamlit
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

#### Deploy with Docker Compose
```bash
# Start all services
docker-compose up -d

# Download models in Ollama container
docker exec ollama-service ollama pull llama3.2:latest
docker exec ollama-service ollama pull gemma:2b

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Production Deployment

### Production Environment Setup

#### System Requirements
```bash
# Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
# Minimum: 4 CPU cores, 8GB RAM, 50GB storage
# Recommended: 8 CPU cores, 16GB RAM, 100GB SSD

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Production Configuration
```env
# Production Environment
APP_TITLE="Ollama Chat Assistant"
APP_ICON="🤖"
PAGE_TITLE="Enterprise AI Chat"

# Ollama Settings
OLLAMA_HOST="http://ollama:11434"
DEFAULT_MODEL="llama3.2:latest"

# Chat Settings
MAX_MESSAGES=100
MAX_MESSAGE_LENGTH=4000

# Knowledge Base Settings
USE_KNOWLEDGE_BASE=true
CHROMA_DB_PATH="/app/data/chroma_db"
MAX_SEARCH_RESULTS=5
CHUNK_SIZE=1000
CHUNK_OVERLAP=100

# Security Settings
ALLOWED_ORIGINS="https://yourdomain.com"
ENABLE_CORS=false
```

### Production Docker Compose

#### docker-compose.prod.yml
```yaml
version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-prod
    ports:
      - "127.0.0.1:11434:11434"
    volumes:
      - /data/ollama:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=http://localhost:8501
    restart: always
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
    
  chat-app:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: chat-app-prod
    ports:
      - "127.0.0.1:8501:8501"
    environment:
      - OLLAMA_HOST=http://ollama:11434
      - CHROMA_DB_PATH=/app/data/chroma_db
      - ENVIRONMENT=production
    volumes:
      - /data/chat_app:/app/data
      - /var/log/chat_app:/app/logs
    depends_on:
      - ollama
    restart: always
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    
  nginx:
    image: nginx:alpine
    container_name: nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - /var/log/nginx:/var/log/nginx
    depends_on:
      - chat-app
    restart: always

  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_SCHEDULE=0 0 4 * * *  # Daily at 4 AM
    restart: always
```

#### Production Nginx Configuration
```nginx
events {
    worker_connections 2048;
}

http {
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    upstream chat_app {
        server chat-app:8501 max_fails=3 fail_timeout=30s;
    }
    
    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name yourdomain.com;
        
        ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        location / {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://chat_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            proxy_pass http://chat_app/_stcore/health;
        }
    }
}
```

### SSL Certificate Setup

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot

# Obtain SSL certificate
sudo certbot certonly --standalone -d yourdomain.com

# Auto-renewal cron job
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## Cloud Deployment

### AWS Deployment

#### ECS Task Definition
```json
{
  "family": "ollama-chat-app",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "2048",
  "memory": "4096",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "chat-app",
      "image": "your-account.dkr.ecr.region.amazonaws.com/ollama-chat-app:latest",
      "portMappings": [
        {
          "containerPort": 8501,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "OLLAMA_HOST",
          "value": "http://ollama-service:11434"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/ollama-chat-app",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Terraform Configuration
```hcl
# main.tf
provider "aws" {
  region = "us-west-2"
}

resource "aws_ecs_cluster" "ollama_cluster" {
  name = "ollama-chat-cluster"
}

resource "aws_ecs_service" "chat_app" {
  name            = "chat-app-service"
  cluster         = aws_ecs_cluster.ollama_cluster.id
  task_definition = aws_ecs_task_definition.chat_app.arn
  desired_count   = 2
  
  network_configuration {
    subnets         = var.subnet_ids
    security_groups = [aws_security_group.chat_app.id]
  }
  
  load_balancer {
    target_group_arn = aws_lb_target_group.chat_app.arn
    container_name   = "chat-app"
    container_port   = 8501
  }
}

resource "aws_lb" "chat_app" {
  name               = "chat-app-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets           = var.subnet_ids
}
```

### Google Cloud Platform Deployment

#### Cloud Run Deployment
```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: ollama-chat-app
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 10
      containers:
      - image: gcr.io/your-project/ollama-chat-app:latest
        ports:
        - containerPort: 8501
        env:
        - name: OLLAMA_HOST
          value: "http://ollama-service:11434"
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "1"
            memory: "2Gi"
```

#### Deploy to Cloud Run
```bash
# Build and push image
gcloud builds submit --tag gcr.io/your-project/ollama-chat-app

# Deploy to Cloud Run
gcloud run deploy ollama-chat-app \
  --image gcr.io/your-project/ollama-chat-app:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 4Gi \
  --cpu 2
```

## Monitoring and Maintenance

### Health Monitoring

#### Health Check Script
```python
#!/usr/bin/env python3
"""Health check script for monitoring application status"""

import requests
import sys
import json
from datetime import datetime

def check_application_health():
    """Check if the application is responding"""
    try:
        response = requests.get('http://localhost:8501/_stcore/health', timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"Application health check failed: {e}")
        return False

def check_ollama_health():
    """Check if Ollama service is responding"""
    try:
        response = requests.get('http://localhost:11434/api/tags', timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"Ollama health check failed: {e}")
        return False

def main():
    """Main health check function"""
    checks = {
        'application': check_application_health(),
        'ollama': check_ollama_health(),
        'timestamp': datetime.now().isoformat()
    }
    
    print(json.dumps(checks, indent=2))
    
    if all(checks.values()):
        sys.exit(0)  # All checks passed
    else:
        sys.exit(1)  # Some checks failed

if __name__ == "__main__":
    main()
```

### Logging Configuration

#### Structured Logging Setup
```python
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_entry)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[
        logging.FileHandler('/var/log/chat_app/app.log'),
        logging.StreamHandler()
    ]
)

# Set JSON formatter
for handler in logging.getLogger().handlers:
    handler.setFormatter(JSONFormatter())
```

### Backup and Recovery

#### Database Backup Script
```bash
#!/bin/bash
# backup_chroma.sh

BACKUP_DIR="/backups/chroma_db"
SOURCE_DIR="/data/chat_app/chroma_db"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create compressed backup
tar -czf "$BACKUP_DIR/chroma_backup_$DATE.tar.gz" -C "$(dirname $SOURCE_DIR)" "$(basename $SOURCE_DIR)"

# Keep only last 7 days of backups
find "$BACKUP_DIR" -name "chroma_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: chroma_backup_$DATE.tar.gz"
```

#### Automated Backup Cron Job
```bash
# Add to crontab
0 2 * * * /opt/scripts/backup_chroma.sh >> /var/log/backup.log 2>&1
```

This deployment guide provides comprehensive instructions for deploying the Ollama Chat Application in various environments, from local development to production cloud deployments.
