# Ollama Chat Application Configuration
# Copy this file to .env and modify the values as needed

# App Settings
APP_TITLE="Ollama Chat Assistant"
APP_ICON="🤖"
PAGE_TITLE="Chat with AI"

# Ollama Settings
OLLAMA_HOST="http://localhost:11434"
DEFAULT_MODEL="llama3.2:latest"

# Chat Settings
MAX_MESSAGES=100
MAX_MESSAGE_LENGTH=4000

# UI Settings
SIDEBAR_WIDTH=300
SHOW_MODEL_INFO=true

# Knowledge Base Settings
USE_KNOWLEDGE_BASE=true
CHROMA_DB_PATH="./chroma_db"
MAX_SEARCH_RESULTS=5
CHUNK_SIZE=1000
CHUNK_OVERLAP=100
