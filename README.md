# Ollama Chat Application 🤖

A production-grade chat application built with Streamlit and Ollama, featuring multiple AI models, session management, and real-time streaming responses.

## Features ✨

- **Multiple AI Models**: Support for various Ollama models with easy selection
- **Real-time Streaming**: Live response streaming for better user experience
- **Session Management**: Persistent chat sessions with message history
- **Production Ready**: Comprehensive testing, CI/CD, and containerization
- **Responsive UI**: Clean and intuitive Streamlit interface
- **Error Handling**: Robust error handling and input validation
- **Configuration Management**: Environment-based configuration
- **Security**: Input sanitization and security best practices

## Available Models 🧠

The application supports the following Ollama models:
- `tazarov/all-minilm-l6-v2-f32:latest` - Embedding model (91 MB)
- `gemma:2b` - Google's Gemma 2B model (1.7 GB)
- `qwen2.5:3b` - Qwen 2.5 3B model (1.9 GB)
- `phi3:latest` - Microsoft Phi-3 model (2.2 GB)
- `llama3.2:latest` - Meta's Llama 3.2 model (2.0 GB)
- `starcoder2:3b` - StarCoder2 3B model (1.7 GB)

## Prerequisites 📋

- Python 3.8 or higher
- [Ollama](https://ollama.ai/) installed and running
- At least one Ollama model downloaded

### Installing Ollama

1. Visit [ollama.ai](https://ollama.ai/) and download Ollama for your platform
2. Install and start Ollama
3. Download models:
   ```bash
   ollama pull llama3.2:latest
   ollama pull gemma:2b
   ollama pull qwen2.5:3b
   ```

## Quick Start 🚀

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ollama-chat-app
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   streamlit run app.py
   ```

4. **Open your browser** and navigate to `http://localhost:8501`

## Development Setup 🛠️

1. **Install development dependencies**:
   ```bash
   pip install -e ".[dev]"
   ```

2. **Run tests**:
   ```bash
   pytest
   ```

3. **Run linting**:
   ```bash
   black .
   flake8 .
   mypy src/
   ```

4. **Run with coverage**:
   ```bash
   pytest --cov=src --cov-report=html
   ```

## Docker Deployment 🐳

1. **Build the Docker image**:
   ```bash
   docker build -t ollama-chat-app .
   ```

2. **Run the container**:
   ```bash
   docker run -p 8501:8501 \
     -e OLLAMA_HOST=http://host.docker.internal:11434 \
     ollama-chat-app
   ```

   > **Note**: Adjust `OLLAMA_HOST` to point to your Ollama instance

## Configuration ⚙️

The application can be configured using environment variables or a `.env` file:

```env
# App Settings
APP_TITLE="Ollama Chat Assistant"
APP_ICON="🤖"
PAGE_TITLE="Chat with AI"

# Ollama Settings
OLLAMA_HOST="http://localhost:11434"
DEFAULT_MODEL="llama3.2:latest"

# Chat Settings
MAX_MESSAGES=100
MAX_MESSAGE_LENGTH=4000

# UI Settings
SIDEBAR_WIDTH=300
SHOW_MODEL_INFO=true
```

## Project Structure 📁

```
ollama-chat-app/
├── app.py                      # Main Streamlit application
├── src/chat_app/              # Main application package
│   ├── __init__.py
│   ├── models.py              # Ollama model management
│   ├── chat_handler.py        # Chat logic and session management
│   ├── ui_components.py       # Reusable UI components
│   └── utils/                 # Utility functions
│       ├── __init__.py
│       ├── config.py          # Configuration management
│       └── validators.py      # Input validation
├── tests/                     # Test directory
│   ├── __init__.py
│   ├── test_models.py
│   └── test_chat_handler.py
├── .github/workflows/         # GitHub Actions
│   ├── ci.yml                 # Continuous Integration
│   └── deploy.yml             # Deployment workflow
├── .streamlit/                # Streamlit configuration
│   └── config.toml
├── requirements.txt           # Python dependencies
├── pyproject.toml            # Project configuration
├── Dockerfile                # Container configuration
└── README.md                 # This file
```

## API Reference 📚

### ChatHandler

Main class for handling chat operations:

```python
from chat_app.chat_handler import chat_handler

# Start a new session
chat_handler.start_new_session("llama3.2:latest")

# Send a message
response = chat_handler.send_message("Hello, how are you?")

# Get current session
session = chat_handler.get_current_session()

# Clear session
chat_handler.clear_session()
```

### OllamaManager

Manages Ollama model interactions:

```python
from chat_app.models import ollama_manager

# Get available models
models = ollama_manager.get_available_models()

# Validate model availability
is_available = ollama_manager.validate_model_availability("llama3.2:latest")

# Get model information
model_info = ollama_manager.get_model_info("llama3.2:latest")
```

## Contributing 🤝

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run the test suite: `pytest`
5. Run linting: `black . && flake8 . && mypy src/`
6. Commit your changes: `git commit -am 'Add feature'`
7. Push to the branch: `git push origin feature-name`
8. Submit a pull request

## Testing 🧪

The project includes comprehensive tests:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_models.py

# Run with verbose output
pytest -v
```

## CI/CD Pipeline 🔄

The project includes GitHub Actions workflows for:

- **Continuous Integration**: Runs tests, linting, and security checks
- **Deployment**: Builds and deploys the application
- **Security Scanning**: Checks for vulnerabilities

## Troubleshooting 🔧

### Common Issues

1. **Ollama not running**:
   - Ensure Ollama is installed and running
   - Check if models are downloaded: `ollama list`

2. **Connection refused**:
   - Verify Ollama host configuration
   - Check firewall settings

3. **Model not found**:
   - Download the model: `ollama pull model-name`
   - Refresh the application

4. **Performance issues**:
   - Use smaller models for faster responses
   - Increase system resources

## License 📄

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments 🙏

- [Ollama](https://ollama.ai/) for providing the AI model infrastructure
- [Streamlit](https://streamlit.io/) for the web application framework
- The open-source community for the various AI models

---

**Happy Chatting!** 🎉
