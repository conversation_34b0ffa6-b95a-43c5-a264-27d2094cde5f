# Ollama Chat Application 🤖

A production-grade chat application built with Streamlit, Ollama, and Lang<PERSON>hain, featuring **privacy-first local knowledge base integration** and AI conversations restricted to your documents only.

## Features ✨

- **🔒 Privacy-First**: Chat exclusively with your local knowledge base - no external knowledge
- **🧠 LangChain Powered**: Built with LangChain for robust AI workflows and document processing
- **📚 Knowledge Base Required**: Cha<PERSON> is disabled until you add documents to ensure privacy
- **🌐 Smart Document Loading**: Automatic parsing of web pages and PDFs using LangChain loaders
- **🔍 Semantic Search**: ChromaDB vector search with LangChain embeddings for relevant content
- **🎯 Context-Aware Responses**: AI answers only based on your uploaded documents with source citations
- **📊 Smart Guidance**: Automatic redirection to Knowledge Base tab when database is empty
- **🤖 Multiple AI Models**: Support for various Ollama models via LangChain integration
- **🎨 Modern UI**: Clean Streamlit interface with clear knowledge base status indicators
- **🛡️ Input Validation**: Robust message validation and error handling
- **⚙️ Production Ready**: Comprehensive testing, CI/CD, and containerization
- **⚡ Real-Time Streaming**: Live AI response generation with animated thinking indicators
- **📋 Advanced Document Management**: Expandable document and chunk views with individual controls
- **🔍 Search in Chunks**: Find specific content across all document chunks
- **🎭 Animated UI**: Living animations and dynamic visual feedback for better user experience
- **📱 Compact Design**: Optimized layouts for efficient content browsing and management
- **🔄 Auto-Recovery**: Intelligent handling of embedding dimension mismatches and errors

## Available Models 🧠

The application supports the following Ollama models:
- `tazarov/all-minilm-l6-v2-f32:latest` - Embedding model (91 MB)
- `gemma:2b` - Google's Gemma 2B model (1.7 GB)
- `qwen2.5:3b` - Qwen 2.5 3B model (1.9 GB)
- `phi3:latest` - Microsoft Phi-3 model (2.2 GB)
- `llama3.2:latest` - Meta's Llama 3.2 model (2.0 GB)
- `starcoder2:3b` - StarCoder2 3B model (1.7 GB)

## Prerequisites 📋

- Python 3.8 or higher
- [Ollama](https://ollama.ai/) installed and running
- At least one Ollama model downloaded
- LangChain and ChromaDB (installed via requirements.txt)

### Installing Ollama

1. Visit [ollama.ai](https://ollama.ai/) and download Ollama for your platform
2. Install and start Ollama
3. Download models:
   ```bash
   ollama pull llama3.2:latest
   ollama pull gemma:2b
   ollama pull qwen2.5:3b
   ```

## Quick Start 🚀

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ollama-chat-app
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   streamlit run app.py
   ```

4. **Open your browser** and navigate to `http://localhost:8501`

## Privacy-First Knowledge Base 📚

**🔒 IMPORTANT**: This application is designed for privacy and will ONLY chat about documents in your local knowledge base. Chat is completely disabled until you add documents.

### Getting Started

1. **Add Your Documents** (Required):
   - The app will automatically guide you to the "Knowledge Base" tab if empty
   - Enter URLs (one per line) for web pages or PDFs you want to chat about
   - Click "Download & Process" to add content using LangChain document loaders
   - Wait for processing to complete

2. **Chat with Your Content**:
   - Once documents are added, the "Chat" tab becomes available
   - Ask questions ONLY about the content you've uploaded
   - The AI will provide answers based exclusively on your knowledge base
   - All responses include source citations from your documents
   - **No external knowledge** is used - only your documents

3. **Manage Your Knowledge Base**:
   - View all stored documents and statistics
   - Delete individual documents or clear all content
   - Monitor knowledge base status in the sidebar

### Supported Content Types (via LangChain)

- **HTML Pages**: LangChain WebBaseLoader automatically extracts clean text content
- **PDF Documents**: LangChain PyPDFLoader extracts text from all pages with metadata
- **Multiple URLs**: Batch processing of multiple documents at once
- **Smart Text Splitting**: LangChain RecursiveCharacterTextSplitter for optimal chunking

## Advanced Features 🚀

### Real-Time Streaming Chat
- **Live Response Generation**: See AI responses appear character by character as they're generated
- **Animated Thinking Indicator**: Beautiful CSS animations show when AI is processing your request
- **Optimized Performance**: Tuned LLM parameters for faster streaming without delays
- **Visual Feedback**: Clear indicators for thinking, typing, and completion states

### Advanced Document Management
- **Expandable Document Views**: Documents displayed in collapsible sections with toggle icons
- **Nested Chunk Organization**: Individual chunks within documents have their own expandable sections
- **Compact Chunk Display**: Optimized layout showing character count, word count, and quick actions
- **Individual Chunk Control**: Delete, view, or manage individual document chunks
- **Search in Chunks**: Find specific content across all document chunks with highlighting

### Intelligent Error Recovery
- **Auto-Dimension Handling**: Automatically detects and fixes embedding dimension mismatches
- **Graceful Degradation**: Continues operation even when individual components fail
- **Smart Collection Recreation**: Rebuilds vector database when needed without data loss
- **Transparent Recovery**: Clear logging and user feedback during recovery operations

### Enhanced User Experience
- **Animated UI Elements**: Living animations for loading states and user interactions
- **Responsive Design**: Optimized layouts that work efficiently with large document collections
- **Smart Navigation**: Automatic guidance to Knowledge Base tab when database is empty
- **Source Transparency**: Every AI response includes citations to specific document sources

### LangChain Prompt Management
- **Structured Templates**: Professional prompt templates using LangChain's PromptTemplate system
- **Consistent Formatting**: Standardized prompt structure across all AI interactions
- **Template Inheritance**: Reusable prompt components for different scenarios
- **Dynamic Context Injection**: Intelligent insertion of knowledge base content and conversation history

## Development Setup 🛠️

1. **Install development dependencies**:
   ```bash
   pip install -e ".[dev]"
   ```

2. **Run tests**:
   ```bash
   pytest
   ```

3. **Run linting**:
   ```bash
   black .
   flake8 .
   mypy src/
   ```

4. **Run with coverage**:
   ```bash
   pytest --cov=src --cov-report=html
   ```

## Docker Deployment 🐳

1. **Build the Docker image**:
   ```bash
   docker build -t ollama-chat-app .
   ```

2. **Run the container**:
   ```bash
   docker run -p 8501:8501 \
     -e OLLAMA_HOST=http://host.docker.internal:11434 \
     ollama-chat-app
   ```

   > **Note**: Adjust `OLLAMA_HOST` to point to your Ollama instance

## Configuration ⚙️

The application can be configured using environment variables or a `.env` file:

```env
# App Settings
APP_TITLE="Ollama Chat Assistant"
APP_ICON="🤖"
PAGE_TITLE="Chat with AI"

# Ollama Settings
OLLAMA_HOST="http://localhost:11434"
DEFAULT_MODEL="llama3.2:latest"

# Chat Settings
MAX_MESSAGES=100
MAX_MESSAGE_LENGTH=4000

# UI Settings
SIDEBAR_WIDTH=300
SHOW_MODEL_INFO=true
```

## Project Structure 📁

```
ollama-chat-app/
├── app.py                      # Main Streamlit application with streaming support
├── src/chat_app/              # Main application package
│   ├── __init__.py
│   ├── models.py              # Ollama model management with LangChain integration
│   ├── chat_handler.py        # Chat logic, session management, and streaming
│   ├── knowledge_base.py      # ChromaDB vector store with auto-recovery
│   ├── web_content.py         # Web content downloading and processing
│   ├── ui_components.py       # Advanced UI components with animations
│   └── utils/                 # Utility functions
│       ├── __init__.py
│       ├── config.py          # Configuration management
│       └── validators.py      # Input validation and sanitization
├── tests/                     # Comprehensive test suite
│   ├── __init__.py
│   ├── test_models.py
│   ├── test_chat_handler.py
│   ├── test_knowledge_base.py
│   └── test_web_content.py
├── docs/                      # Documentation
│   ├── ARCHITECTURE.md        # System architecture
│   ├── USER_GUIDE.md         # Comprehensive user guide
│   └── QUICK_REFERENCE.md    # Quick reference guide
├── .github/workflows/         # GitHub Actions CI/CD
│   ├── ci.yml                 # Continuous Integration
│   └── deploy.yml             # Deployment workflow
├── .streamlit/                # Streamlit configuration
│   └── config.toml
├── chroma_db/                 # Local vector database storage
├── requirements.txt           # Python dependencies
├── pyproject.toml            # Project configuration
├── Dockerfile                # Container configuration
├── ARCHITECTURE.md           # Architecture documentation
├── TECHNICAL_SPECIFICATION.md # Technical specifications
└── README.md                 # This file
```

## API Reference 📚

### ChatHandler

Main class for handling chat operations with streaming support:

```python
from chat_app.chat_handler import chat_handler

# Start a new session
chat_handler.start_new_session("llama3.2:latest")

# Send a message (traditional)
response = chat_handler.send_message("Hello, how are you?")

# Send a message with streaming
for chunk in chat_handler.send_message_streaming("Hello, how are you?"):
    print(chunk, end='', flush=True)

# Send a message with sources
response, sources = chat_handler.send_message_with_sources("Hello, how are you?")

# Get current session
session = chat_handler.get_current_session()

# Clear session
chat_handler.clear_session()
```

### PromptManager (LangChain Integration)

Advanced prompt template management using LangChain:

```python
from chat_app.chat_handler import PromptManager

# Initialize prompt manager
prompt_manager = PromptManager()

# Create context-aware prompt
prompt = prompt_manager.create_context_prompt(
    user_message="What is the thesis process?",
    context_docs=search_results,
    history="Previous conversation context"
)

# Format conversation history
history = prompt_manager.format_conversation_history(messages)

# Access templates directly
kb_template = prompt_manager.knowledge_base_template
no_context_template = prompt_manager.no_context_template
```

### OllamaManager (LangChain Integration)

Manages Ollama model interactions via LangChain:

```python
from chat_app.models import ollama_manager

# Get available models
models = ollama_manager.get_available_models()

# Get LangChain LLM instance
llm = ollama_manager.get_llm("llama3.2:latest")

# Validate model availability
is_available = ollama_manager.validate_model_availability("llama3.2:latest")

# Get model information
model_info = ollama_manager.get_model_info("llama3.2:latest")
```

### Knowledge Base (LangChain + ChromaDB)

Manages document storage and retrieval with auto-recovery:

```python
from chat_app.knowledge_base import knowledge_base

# Add documents
success = knowledge_base.add_document(title, content, url, content_type)

# Search for relevant content
results = knowledge_base.search_content(query, n_results=5)

# Get all documents with metadata
all_docs = knowledge_base.get_all_documents()

# Get knowledge base statistics
stats = knowledge_base.get_stats()

# Delete specific document
success = knowledge_base.delete_document(url)

# Delete individual chunk
success = knowledge_base.delete_chunk(chunk_id)

# Clear entire knowledge base
knowledge_base.clear_all()
```

### Web Content Processor

Handles downloading and processing of web content:

```python
from chat_app.web_content import web_downloader

# Process multiple URLs
results = web_downloader.process_urls([
    "https://example.com/page1",
    "https://example.com/document.pdf"
])

# Validate URLs
valid_urls, invalid_urls = web_downloader.validate_urls(urls)

# Download single URL
content_data = web_downloader.download_content("https://example.com")
```

## Contributing 🤝

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run the test suite: `pytest`
5. Run linting: `black . && flake8 . && mypy src/`
6. Commit your changes: `git commit -am 'Add feature'`
7. Push to the branch: `git push origin feature-name`
8. Submit a pull request

## Testing 🧪

The project includes comprehensive tests:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_models.py

# Run with verbose output
pytest -v
```

## CI/CD Pipeline 🔄

The project includes GitHub Actions workflows for:

- **Continuous Integration**: Runs tests, linting, and security checks
- **Deployment**: Builds and deploys the application
- **Security Scanning**: Checks for vulnerabilities

## Troubleshooting 🔧

### Common Issues

1. **Ollama not running**:
   - Ensure Ollama is installed and running
   - Check if models are downloaded: `ollama list`

2. **Connection refused**:
   - Verify Ollama host configuration
   - Check firewall settings

3. **Model not found**:
   - Download the model: `ollama pull model-name`
   - Refresh the application

4. **Performance issues**:
   - Use smaller models for faster responses
   - Increase system resources

5. **Embedding dimension mismatch**:
   - Application automatically detects and fixes these issues
   - Look for "Dimension mismatch detected" in logs
   - ChromaDB collection will be automatically recreated

6. **Streaming not working**:
   - Check that Ollama supports streaming for your model
   - Verify LangChain integration is properly configured
   - Try refreshing the browser

7. **Document processing failures**:
   - Check URL accessibility and format
   - Verify PDF files are not password-protected
   - Look for detailed error messages in the UI

## License 📄

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments 🙏

- [Ollama](https://ollama.ai/) for providing the AI model infrastructure
- [LangChain](https://langchain.com/) for the powerful AI application framework
- [ChromaDB](https://www.trychroma.com/) for the vector database and embeddings
- [Streamlit](https://streamlit.io/) for the web application framework
- The open-source community for the various AI models and tools

---

**Happy Chatting!** 🎉
