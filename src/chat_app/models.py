"""
Ollama model management and interaction.
"""

import logging
from typing import Dict, List, Optional, Generator
import ollama
from pydantic import BaseModel

from .utils.config import config, get_available_models, validate_model
from .utils.validators import validate_model_name

logger = logging.getLogger(__name__)


class ModelInfo(BaseModel):
    """Model information structure."""
    name: str
    size: Optional[str] = None
    modified: Optional[str] = None
    family: Optional[str] = None
    parameter_size: Optional[str] = None


class OllamaManager:
    """Manages Ollama model interactions."""
    
    def __init__(self):
        self.client = ollama.Client(host=config.ollama_host)
        self._available_models: Optional[List[str]] = None
    
    def get_available_models(self) -> List[str]:
        """
        Get list of available models from Ollama.

        Returns:
            List of available model names
        """
        if self._available_models is None:
            try:
                # Try to get models from Ollama API
                response = self.client.list()
                if hasattr(response, 'models') and response.models:
                    self._available_models = [model.model for model in response.models]
                else:
                    raise ValueError("Invalid response format from Ollama API")
            except Exception as e:
                logger.warning(f"Could not fetch models from Ollama: {e}")
                # Fallback to predefined list
                self._available_models = get_available_models()

        return self._available_models
    
    def validate_model_availability(self, model_name: str) -> bool:
        """
        Check if a model is available.
        
        Args:
            model_name: Name of the model to check
            
        Returns:
            True if model is available, False otherwise
        """
        is_valid, _ = validate_model_name(model_name)
        if not is_valid:
            return False
        
        return model_name in self.get_available_models()
    
    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """
        Get detailed information about a model.

        Args:
            model_name: Name of the model

        Returns:
            ModelInfo object or None if model not found
        """
        try:
            response = self.client.list()
            if hasattr(response, 'models') and response.models:
                for model in response.models:
                    if model.model == model_name:
                        # Convert size from bytes to human readable format
                        size_str = self._format_size(model.size) if model.size else None

                        return ModelInfo(
                            name=model.model,
                            size=size_str,
                            modified=model.modified_at.isoformat() if model.modified_at else None,
                            family=model.details.family if model.details else None,
                            parameter_size=model.details.parameter_size if model.details else None
                        )
        except Exception as e:
            logger.error(f"Error getting model info for {model_name}: {e}")

        return None

    def _format_size(self, size_bytes: int) -> str:
        """
        Format size in bytes to human readable format.

        Args:
            size_bytes: Size in bytes

        Returns:
            Formatted size string
        """
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"

    def generate_response(
        self, 
        model: str, 
        messages: List[Dict[str, str]], 
        stream: bool = True
    ) -> Generator[str, None, None]:
        """
        Generate response from the model.
        
        Args:
            model: Model name to use
            messages: List of message dictionaries
            stream: Whether to stream the response
            
        Yields:
            Response chunks if streaming, otherwise full response
        """
        try:
            if not self.validate_model_availability(model):
                raise ValueError(f"Model {model} is not available")
            
            response = self.client.chat(
                model=model,
                messages=messages,
                stream=stream
            )
            
            if stream:
                for chunk in response:
                    if 'message' in chunk and 'content' in chunk['message']:
                        yield chunk['message']['content']
            else:
                yield response['message']['content']
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            yield f"Error: {str(e)}"


# Global instance
ollama_manager = OllamaManager()
