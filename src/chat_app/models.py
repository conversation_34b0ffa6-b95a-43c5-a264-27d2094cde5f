"""
Ollama model management and interaction using <PERSON><PERSON>hain.
"""

import logging
from typing import Dict, List, Optional
from langchain_ollama import OllamaLLM
from pydantic import BaseModel

from .utils.config import config, get_available_models
from .utils.validators import validate_model_name

logger = logging.getLogger(__name__)


class ModelInfo(BaseModel):
    """Model information structure."""
    name: str
    size: Optional[str] = None
    modified: Optional[str] = None
    family: Optional[str] = None
    parameter_size: Optional[str] = None


class OllamaManager:
    """Manages Ollama model interactions using LangChain."""

    def __init__(self):
        self.base_url = config.ollama_host
        self._available_models: Optional[List[str]] = None
        self._llm_cache: Dict[str, OllamaLLM] = {}
    
    def get_available_models(self) -> List[str]:
        """
        Get list of available models from Ollama.

        Returns:
            List of available model names
        """
        if self._available_models is None:
            try:
                # Use a simple LLM instance to test connectivity and get models
                # LangChain doesn't have a direct list models method, so we use the predefined list
                # and validate connectivity with a test model
                test_llm = OllamaLLM(model="llama3.2:latest", base_url=self.base_url)
                # Try a simple invoke to test connectivity
                test_llm.invoke("test")
                self._available_models = get_available_models()
            except Exception as e:
                logger.warning(f"Could not connect to Ollama: {e}")
                # Fallback to predefined list
                self._available_models = get_available_models()

        return self._available_models
    
    def validate_model_availability(self, model_name: str) -> bool:
        """
        Check if a model is available.

        Args:
            model_name: Name of the model to check

        Returns:
            True if model is available, False otherwise
        """
        is_valid, _ = validate_model_name(model_name)
        if not is_valid:
            return False

        return model_name in self.get_available_models()
    
    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """
        Get basic information about a model.

        Args:
            model_name: Name of the model

        Returns:
            ModelInfo object or None if model not found
        """
        if model_name in self.get_available_models():
            # Return basic info since LangChain doesn't provide detailed model info
            return ModelInfo(
                name=model_name,
                size="Unknown",
                modified=None,
                family=model_name.split(':')[0] if ':' in model_name else model_name,
                parameter_size="Unknown"
            )
        return None

    def get_llm(self, model_name: str) -> OllamaLLM:
        """
        Get or create a LangChain LLM instance for the specified model.

        Args:
            model_name: Name of the model

        Returns:
            OllamaLLM instance
        """
        if model_name not in self._llm_cache:
            self._llm_cache[model_name] = OllamaLLM(
                model=model_name,
                base_url=self.base_url,
                temperature=0.7
            )
        return self._llm_cache[model_name]

    def generate_response(
        self,
        model: str,
        prompt: str
    ) -> str:
        """
        Generate response from the model using LangChain.

        Args:
            model: Model name to use
            prompt: The complete prompt to send to the model

        Returns:
            Generated response string
        """
        try:
            if not self.validate_model_availability(model):
                raise ValueError(f"Model {model} is not available")

            llm = self.get_llm(model)
            response = llm.invoke(prompt)
            return response

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error: {str(e)}"


# Global instance
ollama_manager = OllamaManager()
