"""
Web content downloading and parsing using LangChain document loaders.
"""

import logging
import validators
import re
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
from langchain_community.document_loaders import WebBaseLoader, PyPDFLoader
from langchain_core.documents import Document
import tempfile
import requests

from .utils.config import config

logger = logging.getLogger(__name__)


class ContentType:
    """Content type constants."""
    HTML = "html"
    PDF = "pdf"
    UNKNOWN = "unknown"


class WebContentDownloader:
    """Downloads and parses web content using LangChain document loaders."""

    def __init__(self):
        pass

    def _clean_content(self, content: str) -> str:
        """
        Clean content by removing extra spaces, unwanted content, and normalizing whitespace.

        Args:
            content: Raw content string

        Returns:
            Cleaned content string
        """
        if not content:
            return content

        # Define patterns for unwanted content to remove
        unwanted_patterns = [
            # Cookie consent messages
            r"Oops! This video will not be shown because you have disabled the marketing cookies\. To see the video, accept marketing cookies\.",
            r"This video will not be shown because you have disabled marketing cookies",
            r"To see the video, accept marketing cookies",
            r"Accept marketing cookies to view this content",
            r"Cookie consent required to view this content",

            # Common video/media placeholders
            r"Video content not available",
            r"Media content blocked",
            r"Content blocked by privacy settings",
            r"Enable cookies to view this content",

            # Navigation and UI elements that often get scraped
            r"Skip to main content",
            r"Skip to navigation",
            r"Jump to content",
            r"Back to top",
            r"Scroll to top",

            # Common footer/header noise
            r"Copyright \d{4}.*",
            r"All rights reserved\.?",
            r"Privacy Policy\s*\|?\s*Terms of Service",
            r"Terms and Conditions",

            # Social media noise
            r"Share on Facebook",
            r"Share on Twitter",
            r"Share on LinkedIn",
            r"Follow us on",
            r"Like us on",

            # Advertisement placeholders
            r"Advertisement",
            r"\[AD\]",
            r"\[ADVERTISEMENT\]",
        ]

        # Remove unwanted content patterns
        for pattern in unwanted_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)

        # Remove multiple consecutive spaces (including tabs)
        content = re.sub(r'[ \t]{2,}', ' ', content)

        # Remove multiple consecutive newlines (but preserve paragraph breaks)
        content = re.sub(r'\n{3,}', '\n\n', content)

        # Remove trailing/leading whitespace from each line
        lines = content.split('\n')
        lines = [line.strip() for line in lines if line.strip()]  # Also remove empty lines
        content = '\n'.join(lines)

        # Remove extra whitespace around punctuation
        content = re.sub(r'\s+([,.!?;:])', r'\1', content)
        content = re.sub(r'([,.!?;:])\s+', r'\1 ', content)

        # Normalize quotes and apostrophes
        content = re.sub(r'[""]', '"', content)
        content = re.sub(r'['']', "'", content)

        # Remove empty lines at the beginning and end
        content = content.strip()

        # Final check: if content is too short or only contains noise, return empty
        if len(content.strip()) < 50:  # Less than 50 characters is likely noise
            return ""

        return content

    def _clean_html_content(self, content: str) -> str:
        """
        Additional cleaning specifically for HTML content.

        Args:
            content: HTML content string

        Returns:
            Cleaned content string
        """
        if not content:
            return content

        # HTML-specific unwanted patterns
        html_unwanted_patterns = [
            # JavaScript/CSS remnants
            r'function\s*\([^)]*\)\s*\{[^}]*\}',
            r'var\s+\w+\s*=\s*[^;]+;',
            r'document\.\w+',
            r'window\.\w+',

            # Common web UI elements
            r'Click here to.*',
            r'Read more.*',
            r'Show more.*',
            r'Load more.*',
            r'View all.*',
            r'See all.*',

            # Form elements
            r'Submit',
            r'Reset',
            r'Search\.\.\.',
            r'Enter your.*',
            r'Please enter.*',

            # Navigation breadcrumbs
            r'Home\s*>\s*.*',
            r'You are here:.*',
            r'Breadcrumb.*',

            # Page metadata
            r'Last updated:.*',
            r'Published:.*',
            r'Modified:.*',
            r'Created:.*',
            r'Author:.*',

            # Common web page noise
            r'Print this page',
            r'Email this page',
            r'Bookmark this page',
            r'Add to favorites',

            # Language/accessibility
            r'Switch to.*language',
            r'Change language',
            r'Accessibility.*',
            r'Screen reader.*',
        ]

        # Apply HTML-specific cleaning
        for pattern in html_unwanted_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE)

        # Remove common HTML artifacts
        content = re.sub(r'&nbsp;', ' ', content)
        content = re.sub(r'&amp;', '&', content)
        content = re.sub(r'&lt;', '<', content)
        content = re.sub(r'&gt;', '>', content)
        content = re.sub(r'&quot;', '"', content)

        return content

    def _clean_pdf_content(self, content: str) -> str:
        """
        Additional cleaning specifically for PDF content.

        Args:
            content: PDF content string

        Returns:
            Cleaned content string
        """
        if not content:
            return content

        # PDF-specific unwanted patterns
        pdf_unwanted_patterns = [
            # Page numbers and headers/footers
            r'Page \d+ of \d+',
            r'^\d+$',  # Standalone numbers (often page numbers)
            r'Page \d+',

            # PDF metadata
            r'Created with.*',
            r'Generated by.*',
            r'Printed from.*',
            r'Downloaded from.*',

            # Common PDF artifacts
            r'Table of Contents',
            r'Index',
            r'Bibliography',
            r'References',
            r'Appendix [A-Z]',

            # Watermarks and stamps
            r'CONFIDENTIAL',
            r'DRAFT',
            r'INTERNAL USE ONLY',
            r'FOR REVIEW ONLY',

            # OCR artifacts (common misreads)
            r'\b[A-Z]{10,}\b',  # Long strings of capitals (often OCR errors)
            r'\b\d{10,}\b',     # Long strings of numbers (often OCR errors)

            # PDF form elements
            r'☐',  # Checkbox symbols
            r'☑',  # Checked checkbox symbols
            r'□',  # Empty checkbox
            r'■',  # Filled checkbox
        ]

        # Apply PDF-specific cleaning
        for pattern in pdf_unwanted_patterns:
            content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.MULTILINE)

        # Remove excessive whitespace that's common in PDFs
        content = re.sub(r'\s{5,}', ' ', content)  # 5+ spaces to single space

        # Remove lines that are just punctuation or symbols
        lines = content.split('\n')
        cleaned_lines = []
        for line in lines:
            # Skip lines that are mostly punctuation or very short
            if len(line.strip()) > 3 and not re.match(r'^[^\w\s]*$', line.strip()):
                cleaned_lines.append(line)

        content = '\n'.join(cleaned_lines)

        return content
    
    def validate_urls(self, urls: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate a list of URLs.
        
        Args:
            urls: List of URLs to validate
            
        Returns:
            Tuple of (valid_urls, invalid_urls)
        """
        valid_urls = []
        invalid_urls = []
        
        for url in urls:
            url = url.strip()
            if not url:
                continue
                
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            if validators.url(url):
                valid_urls.append(url)
            else:
                invalid_urls.append(url)
        
        return valid_urls, invalid_urls
    
    def _detect_content_type(self, url: str) -> str:
        """
        Detect content type from URL.

        Args:
            url: The URL

        Returns:
            Content type (html, pdf, or unknown)
        """
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()

        if path.endswith('.pdf'):
            return ContentType.PDF
        else:
            return ContentType.HTML

    def _load_web_content(self, url: str) -> Optional[Dict[str, str]]:
        """
        Load web content using LangChain WebBaseLoader.

        Args:
            url: URL to load

        Returns:
            Dictionary with parsed content or None if failed
        """
        try:
            loader = WebBaseLoader(url)
            documents = loader.load()

            if documents:
                doc = documents[0]
                # Extract title from metadata or use URL
                title = doc.metadata.get('title', url.split('/')[-1] or 'Web Page')

                # Apply HTML-specific cleaning first, then general cleaning
                html_cleaned_content = self._clean_html_content(doc.page_content)
                cleaned_content = self._clean_content(html_cleaned_content)

                # Skip if content is too short after cleaning (likely noise)
                if len(cleaned_content.strip()) < 100:
                    logger.warning(f"Content too short after cleaning for {url}, skipping")
                    return None

                return {
                    'title': title,
                    'content': cleaned_content,
                    'url': url,
                    'content_type': ContentType.HTML
                }

        except Exception as e:
            logger.error(f"Error loading web content from {url}: {e}")

        return None

    def _load_pdf_content(self, url: str) -> Optional[Dict[str, str]]:
        """
        Load PDF content using LangChain PyPDFLoader.

        Args:
            url: URL to PDF

        Returns:
            Dictionary with parsed content or None if failed
        """
        try:
            # Download PDF to temporary file first
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

            try:
                # Use LangChain's PyPDFLoader
                loader = PyPDFLoader(temp_file_path)
                documents = loader.load()

                if documents:
                    # Combine all pages
                    content = '\n\n'.join([doc.page_content for doc in documents])
                    title = f"PDF Document from {url.split('/')[-1]}"

                    # Apply PDF-specific cleaning first, then general cleaning
                    pdf_cleaned_content = self._clean_pdf_content(content)
                    cleaned_content = self._clean_content(pdf_cleaned_content)

                    # Skip if content is too short after cleaning (likely noise)
                    if len(cleaned_content.strip()) < 100:
                        logger.warning(f"PDF content too short after cleaning for {url}, skipping")
                        return None

                    return {
                        'title': title,
                        'content': cleaned_content,
                        'url': url,
                        'content_type': ContentType.PDF
                    }
            finally:
                # Clean up temporary file
                import os
                os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error loading PDF from {url}: {e}")

        return None
    
    def process_urls(self, urls: List[str]) -> Dict[str, any]:
        """
        Process multiple URLs using LangChain document loaders.

        Args:
            urls: List of URLs to process

        Returns:
            Dictionary with processing results
        """
        results = {
            'successful': [],
            'failed': [],
            'invalid_urls': []
        }

        # Validate URLs
        valid_urls, invalid_urls = self.validate_urls(urls)
        results['invalid_urls'] = invalid_urls

        # Process each valid URL
        for url in valid_urls:
            try:
                logger.info(f"Processing URL: {url}")

                # Detect content type and use appropriate loader
                content_type = self._detect_content_type(url)

                if content_type == ContentType.PDF:
                    parsed_content = self._load_pdf_content(url)
                else:
                    parsed_content = self._load_web_content(url)

                if parsed_content:
                    results['successful'].append(parsed_content)
                else:
                    results['failed'].append({'url': url, 'error': 'Content filtered out (too short or contains only noise after cleaning)'})

            except Exception as e:
                logger.error(f"Error processing {url}: {e}")
                results['failed'].append({'url': url, 'error': str(e)})

        return results


# Global instance
web_downloader = WebContentDownloader()
