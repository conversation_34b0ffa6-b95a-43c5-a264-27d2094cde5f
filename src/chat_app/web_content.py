"""
Web content downloading and parsing using LangChain document loaders.
"""

import logging
import validators
import re
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
from langchain_community.document_loaders import WebBaseLoader, PyPDFLoader
from langchain_core.documents import Document
import tempfile
import requests

from .utils.config import config

logger = logging.getLogger(__name__)


class ContentType:
    """Content type constants."""
    HTML = "html"
    PDF = "pdf"
    UNKNOWN = "unknown"


class WebContentDownloader:
    """Downloads and parses web content using LangChain document loaders."""

    def __init__(self):
        pass

    def _clean_content(self, content: str) -> str:
        """
        Clean content by removing double spaces and normalizing whitespace.

        Args:
            content: Raw content string

        Returns:
            Cleaned content string
        """
        if not content:
            return content

        # Remove multiple consecutive spaces (but preserve single spaces)
        content = re.sub(r' {2,}', ' ', content)

        # Remove multiple consecutive newlines (but preserve single newlines)
        content = re.sub(r'\n{3,}', '\n\n', content)

        # Remove trailing/leading whitespace from each line
        lines = content.split('\n')
        lines = [line.strip() for line in lines]
        content = '\n'.join(lines)

        # Remove empty lines at the beginning and end
        content = content.strip()

        return content
    
    def validate_urls(self, urls: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate a list of URLs.
        
        Args:
            urls: List of URLs to validate
            
        Returns:
            Tuple of (valid_urls, invalid_urls)
        """
        valid_urls = []
        invalid_urls = []
        
        for url in urls:
            url = url.strip()
            if not url:
                continue
                
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            if validators.url(url):
                valid_urls.append(url)
            else:
                invalid_urls.append(url)
        
        return valid_urls, invalid_urls
    
    def _detect_content_type(self, url: str) -> str:
        """
        Detect content type from URL.

        Args:
            url: The URL

        Returns:
            Content type (html, pdf, or unknown)
        """
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()

        if path.endswith('.pdf'):
            return ContentType.PDF
        else:
            return ContentType.HTML

    def _load_web_content(self, url: str) -> Optional[Dict[str, str]]:
        """
        Load web content using LangChain WebBaseLoader.

        Args:
            url: URL to load

        Returns:
            Dictionary with parsed content or None if failed
        """
        try:
            loader = WebBaseLoader(url)
            documents = loader.load()

            if documents:
                doc = documents[0]
                # Extract title from metadata or use URL
                title = doc.metadata.get('title', url.split('/')[-1] or 'Web Page')

                # Clean the content before returning
                cleaned_content = self._clean_content(doc.page_content)

                return {
                    'title': title,
                    'content': cleaned_content,
                    'url': url,
                    'content_type': ContentType.HTML
                }

        except Exception as e:
            logger.error(f"Error loading web content from {url}: {e}")

        return None

    def _load_pdf_content(self, url: str) -> Optional[Dict[str, str]]:
        """
        Load PDF content using LangChain PyPDFLoader.

        Args:
            url: URL to PDF

        Returns:
            Dictionary with parsed content or None if failed
        """
        try:
            # Download PDF to temporary file first
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

            try:
                # Use LangChain's PyPDFLoader
                loader = PyPDFLoader(temp_file_path)
                documents = loader.load()

                if documents:
                    # Combine all pages
                    content = '\n\n'.join([doc.page_content for doc in documents])
                    title = f"PDF Document from {url.split('/')[-1]}"

                    # Clean the content before returning
                    cleaned_content = self._clean_content(content)

                    return {
                        'title': title,
                        'content': cleaned_content,
                        'url': url,
                        'content_type': ContentType.PDF
                    }
            finally:
                # Clean up temporary file
                import os
                os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error loading PDF from {url}: {e}")

        return None
    
    def process_urls(self, urls: List[str]) -> Dict[str, any]:
        """
        Process multiple URLs using LangChain document loaders.

        Args:
            urls: List of URLs to process

        Returns:
            Dictionary with processing results
        """
        results = {
            'successful': [],
            'failed': [],
            'invalid_urls': []
        }

        # Validate URLs
        valid_urls, invalid_urls = self.validate_urls(urls)
        results['invalid_urls'] = invalid_urls

        # Process each valid URL
        for url in valid_urls:
            try:
                logger.info(f"Processing URL: {url}")

                # Detect content type and use appropriate loader
                content_type = self._detect_content_type(url)

                if content_type == ContentType.PDF:
                    parsed_content = self._load_pdf_content(url)
                else:
                    parsed_content = self._load_web_content(url)

                if parsed_content:
                    results['successful'].append(parsed_content)
                else:
                    results['failed'].append({'url': url, 'error': 'Failed to load content'})

            except Exception as e:
                logger.error(f"Error processing {url}: {e}")
                results['failed'].append({'url': url, 'error': str(e)})

        return results


# Global instance
web_downloader = WebContentDownloader()
