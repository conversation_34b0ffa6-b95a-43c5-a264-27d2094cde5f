"""
Web content downloading and parsing functionality.
"""

import logging
import requests
import validators
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, urljoin
from pathlib import Path
import tempfile
import os

from bs4 import BeautifulSoup
import html2text
import PyPDF2
import pypdf

from .utils.config import config

logger = logging.getLogger(__name__)


class ContentType:
    """Content type constants."""
    HTML = "html"
    PDF = "pdf"
    UNKNOWN = "unknown"


class WebContentDownloader:
    """Downloads and parses web content from URLs."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.html_converter.ignore_images = True
        self.html_converter.body_width = 0  # Don't wrap lines
    
    def validate_urls(self, urls: List[str]) -> <PERSON>ple[List[str], List[str]]:
        """
        Validate a list of URLs.
        
        Args:
            urls: List of URLs to validate
            
        Returns:
            Tuple of (valid_urls, invalid_urls)
        """
        valid_urls = []
        invalid_urls = []
        
        for url in urls:
            url = url.strip()
            if not url:
                continue
                
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            if validators.url(url):
                valid_urls.append(url)
            else:
                invalid_urls.append(url)
        
        return valid_urls, invalid_urls
    
    def detect_content_type(self, url: str, response: requests.Response) -> str:
        """
        Detect content type from URL and response headers.
        
        Args:
            url: The URL
            response: HTTP response object
            
        Returns:
            Content type (html, pdf, or unknown)
        """
        # Check URL extension
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        if path.endswith('.pdf'):
            return ContentType.PDF
        
        # Check Content-Type header
        content_type = response.headers.get('content-type', '').lower()
        
        if 'application/pdf' in content_type:
            return ContentType.PDF
        elif any(ct in content_type for ct in ['text/html', 'application/xhtml']):
            return ContentType.HTML
        
        # Default to HTML for web pages
        return ContentType.HTML
    
    def download_content(self, url: str) -> Optional[Dict[str, any]]:
        """
        Download content from a URL.
        
        Args:
            url: URL to download from
            
        Returns:
            Dictionary with content info or None if failed
        """
        try:
            logger.info(f"Downloading content from: {url}")
            
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            content_type = self.detect_content_type(url, response)
            
            # Get the full content
            content_bytes = response.content
            
            return {
                'url': url,
                'content_type': content_type,
                'content_bytes': content_bytes,
                'headers': dict(response.headers),
                'status_code': response.status_code
            }
            
        except Exception as e:
            logger.error(f"Error downloading {url}: {e}")
            return None
    
    def parse_html_content(self, content_bytes: bytes, url: str) -> Optional[Dict[str, str]]:
        """
        Parse HTML content and extract text.
        
        Args:
            content_bytes: Raw HTML content
            url: Source URL
            
        Returns:
            Dictionary with parsed content or None if failed
        """
        try:
            # Decode content
            content_str = content_bytes.decode('utf-8', errors='ignore')
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(content_str, 'html.parser')
            
            # Extract title
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No Title"
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Convert to markdown-like text
            markdown_text = self.html_converter.handle(str(soup))
            
            # Clean up the text
            lines = markdown_text.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):  # Keep content, skip empty lines and some headers
                    cleaned_lines.append(line)
            
            content_text = '\n'.join(cleaned_lines)
            
            return {
                'title': title_text,
                'content': content_text,
                'url': url,
                'content_type': ContentType.HTML
            }
            
        except Exception as e:
            logger.error(f"Error parsing HTML from {url}: {e}")
            return None
    
    def parse_pdf_content(self, content_bytes: bytes, url: str) -> Optional[Dict[str, str]]:
        """
        Parse PDF content and extract text.
        
        Args:
            content_bytes: Raw PDF content
            url: Source URL
            
        Returns:
            Dictionary with parsed content or None if failed
        """
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(content_bytes)
                temp_file_path = temp_file.name
            
            try:
                # Try with pypdf first (newer library)
                with open(temp_file_path, 'rb') as file:
                    pdf_reader = pypdf.PdfReader(file)
                    
                    # Extract metadata
                    title = "PDF Document"
                    if pdf_reader.metadata:
                        title = pdf_reader.metadata.get('/Title', title)
                    
                    # Extract text from all pages
                    text_content = []
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text.strip():
                                text_content.append(f"--- Page {page_num + 1} ---\n{page_text}")
                        except Exception as e:
                            logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                    
                    content_text = '\n\n'.join(text_content)
                    
            except Exception as e:
                logger.warning(f"pypdf failed, trying PyPDF2: {e}")
                
                # Fallback to PyPDF2
                with open(temp_file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    # Extract metadata
                    title = "PDF Document"
                    if pdf_reader.metadata:
                        title = pdf_reader.metadata.get('/Title', title)
                    
                    # Extract text from all pages
                    text_content = []
                    for page_num in range(len(pdf_reader.pages)):
                        try:
                            page = pdf_reader.pages[page_num]
                            page_text = page.extract_text()
                            if page_text.strip():
                                text_content.append(f"--- Page {page_num + 1} ---\n{page_text}")
                        except Exception as e:
                            logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                    
                    content_text = '\n\n'.join(text_content)
            
            # Clean up temporary file
            os.unlink(temp_file_path)
            
            if not content_text.strip():
                logger.warning(f"No text content extracted from PDF: {url}")
                return None
            
            return {
                'title': title,
                'content': content_text,
                'url': url,
                'content_type': ContentType.PDF
            }
            
        except Exception as e:
            logger.error(f"Error parsing PDF from {url}: {e}")
            return None
    
    def process_urls(self, urls: List[str]) -> Dict[str, any]:
        """
        Process multiple URLs and extract content.
        
        Args:
            urls: List of URLs to process
            
        Returns:
            Dictionary with processing results
        """
        results = {
            'successful': [],
            'failed': [],
            'invalid_urls': []
        }
        
        # Validate URLs
        valid_urls, invalid_urls = self.validate_urls(urls)
        results['invalid_urls'] = invalid_urls
        
        # Process each valid URL
        for url in valid_urls:
            try:
                # Download content
                download_result = self.download_content(url)
                if not download_result:
                    results['failed'].append({'url': url, 'error': 'Download failed'})
                    continue
                
                # Parse content based on type
                if download_result['content_type'] == ContentType.HTML:
                    parsed_content = self.parse_html_content(
                        download_result['content_bytes'], 
                        url
                    )
                elif download_result['content_type'] == ContentType.PDF:
                    parsed_content = self.parse_pdf_content(
                        download_result['content_bytes'], 
                        url
                    )
                else:
                    results['failed'].append({'url': url, 'error': 'Unsupported content type'})
                    continue
                
                if parsed_content:
                    results['successful'].append(parsed_content)
                else:
                    results['failed'].append({'url': url, 'error': 'Parsing failed'})
                    
            except Exception as e:
                logger.error(f"Error processing {url}: {e}")
                results['failed'].append({'url': url, 'error': str(e)})
        
        return results


# Global instance
web_downloader = WebContentDownloader()
