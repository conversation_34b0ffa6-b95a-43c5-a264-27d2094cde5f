"""
Knowledge base management using ChromaDB for storing and retrieving web content.
"""

import logging
import hashlib
import uuid
from typing import List, Dict, Optional, Any
from datetime import datetime
import chromadb
from chromadb.config import Settings

from .utils.config import config

logger = logging.getLogger(__name__)


class KnowledgeBase:
    """Manages the ChromaDB knowledge base for storing web content."""
    
    def __init__(self, persist_directory: str = "./chroma_db"):
        """
        Initialize the knowledge base.
        
        Args:
            persist_directory: Directory to persist ChromaDB data
        """
        self.persist_directory = persist_directory
        self.client = None
        self.collection = None
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize ChromaDB client and collection."""
        try:
            # Initialize ChromaDB client with persistence
            self.client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            self.collection = self.client.get_or_create_collection(
                name="web_content",
                metadata={"description": "Web content from URLs for chat context"}
            )
            
            logger.info(f"ChromaDB initialized with {self.collection.count()} documents")
            
        except Exception as e:
            logger.error(f"Error initializing ChromaDB: {e}")
            raise
    
    def _generate_document_id(self, url: str, content: str) -> str:
        """
        Generate a unique document ID based on URL and content hash.
        
        Args:
            url: Source URL
            content: Document content
            
        Returns:
            Unique document ID
        """
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()[:8]
        return f"{url_hash}_{content_hash}"
    
    def _chunk_content(self, content: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Split content into overlapping chunks for better retrieval.
        
        Args:
            content: Text content to chunk
            chunk_size: Maximum size of each chunk
            overlap: Number of characters to overlap between chunks
            
        Returns:
            List of content chunks
        """
        if len(content) <= chunk_size:
            return [content]
        
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            
            # Try to break at a sentence or paragraph boundary
            if end < len(content):
                # Look for sentence endings
                for i in range(end, max(start + chunk_size // 2, end - 200), -1):
                    if content[i] in '.!?\n':
                        end = i + 1
                        break
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(content):
                break
        
        return chunks
    
    def add_document(self, title: str, content: str, url: str, content_type: str) -> bool:
        """
        Add a document to the knowledge base.
        
        Args:
            title: Document title
            content: Document content
            url: Source URL
            content_type: Type of content (html, pdf)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not content.strip():
                logger.warning(f"Empty content for URL: {url}")
                return False
            
            # Check if document already exists
            existing_docs = self.collection.get(
                where={"url": url}
            )
            
            if existing_docs['ids']:
                logger.info(f"Document from {url} already exists, updating...")
                # Remove existing documents from this URL
                self.collection.delete(
                    where={"url": url}
                )
            
            # Chunk the content
            chunks = self._chunk_content(content)
            
            # Prepare data for insertion
            ids = []
            documents = []
            metadatas = []
            
            for i, chunk in enumerate(chunks):
                doc_id = f"{self._generate_document_id(url, chunk)}_{i}"
                ids.append(doc_id)
                documents.append(chunk)
                metadatas.append({
                    "title": title,
                    "url": url,
                    "content_type": content_type,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "added_at": datetime.now().isoformat()
                })
            
            # Add to collection
            self.collection.add(
                ids=ids,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Added {len(chunks)} chunks from {url} to knowledge base")
            return True
            
        except Exception as e:
            logger.error(f"Error adding document from {url}: {e}")
            return False
    
    def search_content(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        Search for relevant content in the knowledge base.
        
        Args:
            query: Search query
            n_results: Number of results to return
            
        Returns:
            List of relevant documents with metadata
        """
        try:
            if not query.strip():
                return []
            
            # Search in the collection
            results = self.collection.query(
                query_texts=[query],
                n_results=min(n_results, self.collection.count())
            )
            
            # Format results
            formatted_results = []
            if results['ids'] and results['ids'][0]:
                for i in range(len(results['ids'][0])):
                    formatted_results.append({
                        'id': results['ids'][0][i],
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i] if results['distances'] else None
                    })
            
            logger.info(f"Found {len(formatted_results)} relevant documents for query: {query[:50]}...")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching content: {e}")
            return []
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """
        Get all documents in the knowledge base.
        
        Returns:
            List of all documents with metadata
        """
        try:
            # Get all documents
            results = self.collection.get()
            
            # Group by URL to avoid duplicates
            url_docs = {}
            for i, doc_id in enumerate(results['ids']):
                metadata = results['metadatas'][i]
                url = metadata['url']
                
                if url not in url_docs:
                    url_docs[url] = {
                        'url': url,
                        'title': metadata['title'],
                        'content_type': metadata['content_type'],
                        'added_at': metadata['added_at'],
                        'chunk_count': metadata['total_chunks']
                    }
            
            return list(url_docs.values())
            
        except Exception as e:
            logger.error(f"Error getting all documents: {e}")
            return []
    
    def delete_document(self, url: str) -> bool:
        """
        Delete all chunks of a document by URL.
        
        Args:
            url: URL of the document to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete all chunks from this URL
            self.collection.delete(
                where={"url": url}
            )
            
            logger.info(f"Deleted document from {url}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document from {url}: {e}")
            return False
    
    def clear_all(self) -> bool:
        """
        Clear all documents from the knowledge base.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete the collection and recreate it
            self.client.delete_collection("web_content")
            self.collection = self.client.get_or_create_collection(
                name="web_content",
                metadata={"description": "Web content from URLs for chat context"}
            )
            
            logger.info("Cleared all documents from knowledge base")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing knowledge base: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the knowledge base.
        
        Returns:
            Dictionary with statistics
        """
        try:
            total_chunks = self.collection.count()
            documents = self.get_all_documents()
            
            content_types = {}
            for doc in documents:
                content_type = doc['content_type']
                content_types[content_type] = content_types.get(content_type, 0) + 1
            
            return {
                'total_documents': len(documents),
                'total_chunks': total_chunks,
                'content_types': content_types,
                'avg_chunks_per_doc': total_chunks / len(documents) if documents else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}


# Global instance
knowledge_base = KnowledgeBase()
