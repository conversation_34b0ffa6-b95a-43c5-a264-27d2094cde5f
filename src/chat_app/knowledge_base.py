"""
Knowledge base management using LangChain's ChromaDB integration.
"""

import logging
from typing import List, Dict, Optional, Any
from datetime import datetime
from langchain_chroma import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

from .utils.config import config

logger = logging.getLogger(__name__)


class KnowledgeBase:
    """Manages the ChromaDB knowledge base using Lang<PERSON>hain."""

    def __init__(self, persist_directory: str = "./chroma_db"):
        """
        Initialize the knowledge base.

        Args:
            persist_directory: Directory to persist ChromaDB data
        """
        self.persist_directory = persist_directory
        # Use a simple embedding model that's more likely to be available
        self.embeddings = OllamaEmbeddings(
            model="llama3.2:latest",
            base_url=config.ollama_host
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.chunk_size,
            chunk_overlap=config.chunk_overlap,
            separators=["\n\n", "\n", " ", ""]
        )
        self.vectorstore = None
        self._initialize_db()

    def _initialize_db(self):
        """Initialize ChromaDB vector store."""
        try:
            # Initialize LangChain Chroma vector store
            self.vectorstore = Chroma(
                persist_directory=self.persist_directory,
                embedding_function=self.embeddings,
                collection_name="web_content"
            )

            logger.info(f"ChromaDB initialized with LangChain integration")

        except Exception as e:
            logger.error(f"Error initializing ChromaDB: {e}")
            raise
    
    def _create_documents(self, title: str, content: str, url: str, content_type: str) -> List[Document]:
        """
        Create LangChain Document objects from content.

        Args:
            title: Document title
            content: Text content
            url: Source URL
            content_type: Type of content

        Returns:
            List of Document objects
        """
        # Split content into chunks using LangChain's text splitter
        chunks = self.text_splitter.split_text(content)

        documents = []
        for i, chunk in enumerate(chunks):
            doc = Document(
                page_content=chunk,
                metadata={
                    "title": title,
                    "url": url,
                    "content_type": content_type,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "added_at": datetime.now().isoformat()
                }
            )
            documents.append(doc)

        return documents
    
    def add_document(self, title: str, content: str, url: str, content_type: str) -> bool:
        """
        Add a document to the knowledge base using LangChain.

        Args:
            title: Document title
            content: Document content
            url: Source URL
            content_type: Type of content (html, pdf)

        Returns:
            True if successful, False otherwise
        """
        try:
            if not content.strip():
                logger.warning(f"Empty content for URL: {url}")
                return False

            # Delete existing documents from this URL
            self.delete_document(url)

            # Create Document objects
            documents = self._create_documents(title, content, url, content_type)

            # Add documents to vector store
            self.vectorstore.add_documents(documents)

            logger.info(f"Added {len(documents)} chunks from {url} to knowledge base")
            return True

        except Exception as e:
            logger.error(f"Error adding document from {url}: {e}")
            return False
    
    def search_content(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        Search for relevant content using LangChain similarity search.

        Args:
            query: Search query (empty string returns all chunks)
            n_results: Number of results to return

        Returns:
            List of relevant documents with metadata
        """
        try:
            # If query is empty, get all chunks using ChromaDB directly
            if not query.strip():
                collection = self.vectorstore._collection
                all_results = collection.get(limit=n_results)

                if all_results and all_results['ids']:
                    formatted_results = []
                    for i, chunk_id in enumerate(all_results['ids']):
                        formatted_results.append({
                            'id': chunk_id,
                            'content': all_results['documents'][i] if all_results['documents'] else '',
                            'metadata': all_results['metadatas'][i] if all_results['metadatas'] else {},
                            'distance': 0.0  # No distance for "get all" queries
                        })

                    logger.info(f"Retrieved {len(formatted_results)} chunks (all chunks query)")
                    return formatted_results
                else:
                    return []

            # Use LangChain's similarity search with scores for actual queries
            docs_with_scores = self.vectorstore.similarity_search_with_score(
                query,
                k=n_results
            )

            # Format results
            formatted_results = []
            collection = self.vectorstore._collection

            for doc, score in docs_with_scores:
                # Try to get the document ID from ChromaDB using exact content match
                doc_id = None
                try:
                    # Get all documents and find exact match
                    all_results = collection.get()
                    if all_results and all_results['documents']:
                        for i, stored_content in enumerate(all_results['documents']):
                            if stored_content == doc.page_content:
                                doc_id = all_results['ids'][i]
                                break
                except Exception as e:
                    logger.warning(f"Could not retrieve chunk ID: {e}")

                formatted_results.append({
                    'id': doc_id,
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'distance': score  # Lower score means more similar
                })

            logger.info(f"Found {len(formatted_results)} relevant documents for query: {query[:50]}...")
            return formatted_results

        except Exception as e:
            logger.error(f"Error searching content: {e}")
            return []
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """
        Get all unique documents in the knowledge base.

        Returns:
            List of all documents with metadata
        """
        try:
            # Get all documents from the vector store
            # Since LangChain doesn't have a direct "get all" method, we'll use a broad search
            all_docs = self.vectorstore.similarity_search("", k=1000)  # Get up to 1000 docs

            # Group by URL to avoid duplicates
            url_docs = {}
            for doc in all_docs:
                metadata = doc.metadata
                url = metadata.get('url', 'unknown')

                if url not in url_docs:
                    url_docs[url] = {
                        'url': url,
                        'title': metadata.get('title', 'Unknown'),
                        'content_type': metadata.get('content_type', 'unknown'),
                        'added_at': metadata.get('added_at', ''),
                        'chunk_count': metadata.get('total_chunks', 1)
                    }

            return list(url_docs.values())

        except Exception as e:
            logger.error(f"Error getting all documents: {e}")
            return []
    
    def delete_document(self, url: str) -> bool:
        """
        Delete all chunks of a document by URL.

        Args:
            url: URL of the document to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the underlying ChromaDB collection
            collection = self.vectorstore._collection

            # Get all documents with this URL
            results = collection.get(
                where={"url": url}
            )

            if results and results['ids']:
                # Delete documents by their IDs
                collection.delete(ids=results['ids'])
                logger.info(f"Deleted {len(results['ids'])} chunks from {url}")
                return True
            else:
                logger.warning(f"No documents found for URL: {url}")
                return False

        except Exception as e:
            logger.error(f"Error deleting document from {url}: {e}")
            return False

    def clear_all(self) -> bool:
        """
        Clear all documents from the knowledge base.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the underlying ChromaDB collection
            collection = self.vectorstore._collection

            # Get all document IDs
            all_results = collection.get()

            if all_results and all_results['ids']:
                # Delete all documents
                collection.delete(ids=all_results['ids'])
                logger.info(f"Cleared {len(all_results['ids'])} documents from knowledge base")
            else:
                logger.info("Knowledge base was already empty")

            return True

        except Exception as e:
            logger.error(f"Error clearing knowledge base: {e}")
            return False

    def delete_chunk(self, chunk_id: str) -> bool:
        """
        Delete a specific chunk by its ID.

        Args:
            chunk_id: ID of the chunk to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the underlying ChromaDB collection
            collection = self.vectorstore._collection

            # Delete the specific chunk by ID
            collection.delete(ids=[chunk_id])
            logger.info(f"Deleted chunk with ID: {chunk_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting chunk {chunk_id}: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the knowledge base.

        Returns:
            Dictionary with statistics
        """
        try:
            documents = self.get_all_documents()

            content_types = {}
            total_chunks = 0

            for doc in documents:
                content_type = doc['content_type']
                content_types[content_type] = content_types.get(content_type, 0) + 1
                total_chunks += doc.get('chunk_count', 1)

            return {
                'total_documents': len(documents),
                'total_chunks': total_chunks,
                'content_types': content_types,
                'avg_chunks_per_doc': total_chunks / len(documents) if documents else 0
            }

        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}


# Global instance
knowledge_base = KnowledgeBase()
