"""
Chat session management and message handling using <PERSON><PERSON><PERSON><PERSON>.
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from .models import ollama_manager
from .knowledge_base import knowledge_base
from .utils.config import config
from .utils.validators import validate_message, sanitize_message

logger = logging.getLogger(__name__)


class ChatMessage:
    """Represents a chat message."""
    
    def __init__(self, role: str, content: str, timestamp: Optional[datetime] = None):
        self.role = role  # 'user' or 'assistant'
        self.content = content
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary format for Ollama API."""
        return {
            "role": self.role,
            "content": self.content
        }


class ChatSession:
    """Manages a chat session with message history."""
    
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.messages: List[ChatMessage] = []
        self.created_at = datetime.now()
    
    def add_message(self, role: str, content: str) -> bool:
        """
        Add a message to the session.
        
        Args:
            role: 'user' or 'assistant'
            content: Message content
            
        Returns:
            True if message was added successfully
        """
        try:
            # Validate and sanitize content
            if role == 'user':
                is_valid, error = validate_message(content, config.max_message_length)
                if not is_valid:
                    logger.warning(f"Invalid message: {error}")
                    return False
                content = sanitize_message(content)
            
            message = ChatMessage(role, content)
            self.messages.append(message)
            
            # Limit message history
            if len(self.messages) > config.max_messages:
                self.messages = self.messages[-config.max_messages:]
            
            return True
        except Exception as e:
            logger.error(f"Error adding message: {e}")
            return False
    
    def get_messages_for_api(self) -> List[Dict[str, str]]:
        """Get messages in format suitable for Ollama API."""
        return [msg.to_dict() for msg in self.messages]
    
    def clear_history(self):
        """Clear all messages from the session."""
        self.messages.clear()
    
    def get_message_count(self) -> int:
        """Get total number of messages in session."""
        return len(self.messages)


class ChatHandler:
    """Handles chat operations and session management."""
    
    def __init__(self):
        self.current_session: Optional[ChatSession] = None
    
    def start_new_session(self, model_name: str) -> bool:
        """
        Start a new chat session.
        
        Args:
            model_name: Name of the model to use
            
        Returns:
            True if session started successfully
        """
        try:
            if not ollama_manager.validate_model_availability(model_name):
                logger.error(f"Model {model_name} is not available")
                return False
            
            self.current_session = ChatSession(model_name)
            logger.info(f"Started new session with model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Error starting new session: {e}")
            return False
    
    def send_message(self, user_message: str) -> Optional[str]:
        """
        Send a user message and get AI response using only the knowledge base.

        Args:
            user_message: The user's message

        Returns:
            AI response or None if error occurred
        """
        if not self.current_session:
            logger.error("No active session")
            return None

        try:
            # Add user message
            if not self.current_session.add_message('user', user_message):
                return None

            # Check if knowledge base has content
            kb_stats = knowledge_base.get_stats()
            if kb_stats.get('total_documents', 0) == 0:
                return "I can only answer questions based on documents in the knowledge base. Please add some documents first in the Knowledge Base tab. You can get started quickly by clicking '📋 Load Sample URLs' to load JAMK thesis guidelines, or enter your own URLs."

            # Search knowledge base for relevant context - REQUIRED
            try:
                search_results = knowledge_base.search_content(
                    user_message,
                    n_results=config.max_search_results
                )

                if not search_results:
                    return "I couldn't find any relevant information in the knowledge base to answer your question. Please try rephrasing your question or add more relevant documents."

                context_docs = search_results

            except Exception as e:
                logger.error(f"Error searching knowledge base: {e}")
                return "Error searching the knowledge base. Please try again."

            # Create prompt with context - enforce knowledge base only
            prompt = self._create_prompt_with_context(user_message, context_docs)

            # Get LLM and generate response
            llm = ollama_manager.get_llm(self.current_session.model_name)
            response = llm.invoke(prompt)

            # Add AI response to session
            if response:
                self.current_session.add_message('assistant', response)
                return response

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return f"Error: {str(e)}"

        return None

    def send_message_streaming(self, user_message: str):
        """
        Send a user message and get streaming AI response.

        Args:
            user_message: The user's message

        Yields:
            Chunks of AI response as they are generated
        """
        if not self.current_session:
            logger.error("No active session")
            yield "Error: No active session"
            return

        try:
            # Add user message
            if not self.current_session.add_message('user', user_message):
                yield "Error: Failed to add message to session"
                return

            # Check if knowledge base has content
            kb_stats = knowledge_base.get_stats()
            if kb_stats.get('total_documents', 0) == 0:
                yield "I can only answer questions based on documents in the knowledge base. Please add some documents first in the Knowledge Base tab. You can get started quickly by clicking '📋 Load Sample URLs' to load JAMK thesis guidelines, or enter your own URLs."
                return

            # Search knowledge base for relevant context - REQUIRED
            try:
                search_results = knowledge_base.search_content(
                    user_message,
                    n_results=config.max_search_results
                )

                if not search_results:
                    yield "I couldn't find any relevant information in the knowledge base to answer your question. Please try rephrasing your question or add more relevant documents."
                    return

                context_docs = search_results

            except Exception as e:
                logger.error(f"Error searching knowledge base: {e}")
                yield "Error searching the knowledge base. Please try again."
                return

            # Create prompt with context - enforce knowledge base only
            prompt = self._create_prompt_with_context(user_message, context_docs)

            # Get LLM and generate streaming response
            llm = ollama_manager.get_llm(self.current_session.model_name)

            # Collect the full response for session storage
            full_response = ""

            # Stream the response
            for chunk in llm.stream(prompt):
                if chunk:
                    full_response += chunk
                    yield chunk

            # Add AI response to session after streaming is complete
            if full_response:
                self.current_session.add_message('assistant', full_response)

        except Exception as e:
            logger.error(f"Error sending streaming message: {e}")
            yield f"Error: {str(e)}"

    def send_message_with_sources(self, user_message: str) -> Tuple[Optional[str], List[Dict]]:
        """
        Send a user message and get AI response with source documents.

        Args:
            user_message: The user's message

        Returns:
            Tuple of (AI response or None, list of source documents used)
        """
        if not self.current_session:
            logger.error("No active session")
            return None, []

        try:
            # Add user message
            if not self.current_session.add_message('user', user_message):
                return None, []

            # Check if knowledge base has content
            kb_stats = knowledge_base.get_stats()
            if kb_stats.get('total_documents', 0) == 0:
                return "I can only answer questions based on documents in the knowledge base. Please add some documents first in the Knowledge Base tab. You can get started quickly by clicking '📋 Load Sample URLs' to load JAMK thesis guidelines, or enter your own URLs.", []

            top_n = 5
            # Search knowledge base for relevant context - REQUIRED
            try:
                search_results = knowledge_base.search_content(
                    user_message,
                    n_results=top_n  # Get top_n documents for display
                )

                if not search_results:
                    return "I couldn't find any relevant information in the knowledge base to answer your question. Please try rephrasing your question or add more relevant documents.", []

                context_docs = search_results

            except Exception as e:
                logger.error(f"Error searching knowledge base: {e}")
                return "Error searching the knowledge base. Please try again.", []

            # Create prompt with context - enforce knowledge base only
            prompt = self._create_prompt_with_context(user_message, context_docs)

            # Get LLM and generate response
            llm = ollama_manager.get_llm(self.current_session.model_name)
            response = llm.invoke(prompt)

            # Add AI response to session
            if response:
                self.current_session.add_message('assistant', response)
                return response, context_docs
            else:
                return None, context_docs

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return f"Error: {str(e)}", []

    def _create_prompt_with_context(self, user_message: str, context_docs: List[Dict]) -> str:
        """
        Create a prompt with knowledge base context using LangChain.

        Args:
            user_message: The user's message
            context_docs: Relevant documents from knowledge base

        Returns:
            Complete prompt string
        """
        # Build context section
        context_section = ""
        if context_docs:
            context_text = "\n\n".join([
                f"Source: {doc['metadata']['title']} ({doc['metadata']['url']})\n{doc['content']}"
                for doc in context_docs
            ])

            context_section = f"""KNOWLEDGE BASE CONTENT:
{context_text}

STRICT INSTRUCTIONS:
- You MUST answer ONLY based on the provided knowledge base content above
- DO NOT use any external knowledge or general information
- If the answer is not in the provided content, say "I don't have information about this in the knowledge base"
- Always cite the source URL when referencing information
- Be concise and accurate
- Only reference information that is explicitly stated in the knowledge base content

"""

        # Build conversation history
        history_section = ""
        if len(self.current_session.messages) > 1:  # More than just the current message
            history_section = "Previous conversation:\n"
            for msg in self.current_session.messages[:-1]:  # Exclude current message
                history_section += f"{msg.role.title()}: {msg.content}\n"
            history_section += "\n"

        # Combine all parts
        full_prompt = f"""{context_section}{history_section}Human: {user_message}"""
        return full_prompt

    def get_current_session(self) -> Optional[ChatSession]:
        """Get the current chat session."""
        return self.current_session

    def clear_session(self):
        """Clear the current session."""
        if self.current_session:
            self.current_session.clear_history()


# Global chat handler instance
chat_handler = ChatHandler()
