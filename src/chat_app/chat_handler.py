"""
Chat session management and message handling.
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime
import streamlit as st

from .models import ollama_manager
from .utils.config import config
from .utils.validators import validate_message, sanitize_message

logger = logging.getLogger(__name__)


class ChatMessage:
    """Represents a chat message."""
    
    def __init__(self, role: str, content: str, timestamp: Optional[datetime] = None):
        self.role = role  # 'user' or 'assistant'
        self.content = content
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary format for Ollama API."""
        return {
            "role": self.role,
            "content": self.content
        }


class ChatSession:
    """Manages a chat session with message history."""
    
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.messages: List[ChatMessage] = []
        self.created_at = datetime.now()
    
    def add_message(self, role: str, content: str) -> bool:
        """
        Add a message to the session.
        
        Args:
            role: 'user' or 'assistant'
            content: Message content
            
        Returns:
            True if message was added successfully
        """
        try:
            # Validate and sanitize content
            if role == 'user':
                is_valid, error = validate_message(content, config.max_message_length)
                if not is_valid:
                    logger.warning(f"Invalid message: {error}")
                    return False
                content = sanitize_message(content)
            
            message = ChatMessage(role, content)
            self.messages.append(message)
            
            # Limit message history
            if len(self.messages) > config.max_messages:
                self.messages = self.messages[-config.max_messages:]
            
            return True
        except Exception as e:
            logger.error(f"Error adding message: {e}")
            return False
    
    def get_messages_for_api(self) -> List[Dict[str, str]]:
        """Get messages in format suitable for Ollama API."""
        return [msg.to_dict() for msg in self.messages]
    
    def clear_history(self):
        """Clear all messages from the session."""
        self.messages.clear()
    
    def get_message_count(self) -> int:
        """Get total number of messages in session."""
        return len(self.messages)


class ChatHandler:
    """Handles chat operations and session management."""
    
    def __init__(self):
        self.current_session: Optional[ChatSession] = None
    
    def start_new_session(self, model_name: str) -> bool:
        """
        Start a new chat session.
        
        Args:
            model_name: Name of the model to use
            
        Returns:
            True if session started successfully
        """
        try:
            if not ollama_manager.validate_model_availability(model_name):
                logger.error(f"Model {model_name} is not available")
                return False
            
            self.current_session = ChatSession(model_name)
            logger.info(f"Started new session with model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Error starting new session: {e}")
            return False
    
    def send_message(self, user_message: str) -> Optional[str]:
        """
        Send a user message and get AI response.
        
        Args:
            user_message: The user's message
            
        Returns:
            AI response or None if error occurred
        """
        if not self.current_session:
            logger.error("No active session")
            return None
        
        try:
            # Add user message
            if not self.current_session.add_message('user', user_message):
                return None
            
            # Get AI response
            messages = self.current_session.get_messages_for_api()
            response_generator = ollama_manager.generate_response(
                self.current_session.model_name,
                messages,
                stream=True
            )
            
            # Collect full response
            full_response = ""
            for chunk in response_generator:
                full_response += chunk
            
            # Add AI response to session
            if full_response:
                self.current_session.add_message('assistant', full_response)
                return full_response
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return f"Error: {str(e)}"
        
        return None
    
    def get_current_session(self) -> Optional[ChatSession]:
        """Get the current chat session."""
        return self.current_session
    
    def clear_session(self):
        """Clear the current session."""
        if self.current_session:
            self.current_session.clear_history()


# Global chat handler instance
chat_handler = ChatHandler()
