"""
Input validation utilities for the chat application.
"""

import re
from typing import Op<PERSON>, <PERSON><PERSON>


def validate_message(message: str, max_length: int = 4000) -> Tuple[bool, Optional[str]]:
    """
    Validate user message input.
    
    Args:
        message: The message to validate
        max_length: Maximum allowed message length
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not message or not message.strip():
        return False, "Message cannot be empty"
    
    if len(message) > max_length:
        return False, f"Message too long. Maximum {max_length} characters allowed"
    
    # Check for potentially harmful content (basic check)
    harmful_patterns = [
        r'<script.*?>.*?</script>',
        r'javascript:',
        r'data:text/html',
    ]
    
    for pattern in harmful_patterns:
        if re.search(pattern, message, re.IGNORECASE):
            return False, "Message contains potentially harmful content"
    
    return True, None


def sanitize_message(message: str) -> str:
    """
    Sanitize user message by removing potentially harmful content.
    
    Args:
        message: The message to sanitize
        
    Returns:
        Sanitized message
    """
    # Remove HTML tags
    message = re.sub(r'<[^>]+>', '', message)
    
    # Remove excessive whitespace
    message = re.sub(r'\s+', ' ', message).strip()
    
    return message


def validate_model_name(model_name: str) -> Tuple[bool, Optional[str]]:
    """
    Validate model name format.
    
    Args:
        model_name: The model name to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not model_name:
        return False, "Model name cannot be empty"
    
    # Basic model name pattern validation
    if not re.match(r'^[a-zA-Z0-9._:-]+$', model_name):
        return False, "Invalid model name format"
    
    return True, None
