"""
Configuration management for the chat application.
"""

import os
from typing import List, Optional
from pydantic import Field, ConfigDict
from pydantic_settings import BaseSettings


class AppConfig(BaseSettings):
    """Application configuration settings."""
    
    # App settings
    app_title: str = Field(default="Ollama Chat Assistant", env="APP_TITLE")
    app_icon: str = Field(default="🤖", env="APP_ICON")
    page_title: str = Field(default="Chat with AI", env="PAGE_TITLE")
    
    # Ollama settings
    ollama_host: str = Field(default="http://localhost:11434", env="OLLAMA_HOST")
    default_model: str = Field(default="llama3.2:latest", env="DEFAULT_MODEL")
    
    # Chat settings
    max_messages: int = Field(default=100, env="MAX_MESSAGES")
    max_message_length: int = Field(default=4000, env="MAX_MESSAGE_LENGTH")
    
    # UI settings
    sidebar_width: int = Field(default=300, env="SIDEBAR_WIDTH")
    show_model_info: bool = Field(default=True, env="SHOW_MODEL_INFO")
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )


# Global configuration instance
config = AppConfig()


def get_available_models() -> List[str]:
    """
    Get list of available Ollama models.
    
    Returns:
        List of model names
    """
    # These are the models we found from 'ollama list'
    return [
        "tazarov/all-minilm-l6-v2-f32:latest",
        "gemma:2b",
        "qwen2.5:3b", 
        "phi3:latest",
        "llama3.2:latest",
        "starcoder2:3b"
    ]


def validate_model(model_name: str) -> bool:
    """
    Validate if a model is available.
    
    Args:
        model_name: Name of the model to validate
        
    Returns:
        True if model is available, False otherwise
    """
    return model_name in get_available_models()
