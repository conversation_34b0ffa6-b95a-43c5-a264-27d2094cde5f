"""
Reusable UI components for the chat application.
"""

import streamlit as st
from typing import List, Optional, Dict
from datetime import datetime

from .models import ollama_manager
from .chat_handler import ChatSession
from .web_content import web_downloader
from .knowledge_base import knowledge_base
from .utils.config import config


def render_sidebar() -> Optional[str]:
    """
    Render the sidebar with model selection and settings.
    
    Returns:
        Selected model name or None
    """
    with st.sidebar:
        st.title("🤖 Chat Settings")
        
        # Model selection
        st.subheader("Model Selection")
        available_models = ollama_manager.get_available_models()
        
        if not available_models:
            st.error("No models available. Please ensure Ollama is running.")
            return None
        
        # Set default model index
        default_index = 0
        if config.default_model in available_models:
            default_index = available_models.index(config.default_model)
        
        selected_model = st.selectbox(
            "Choose a model:",
            available_models,
            index=default_index,
            help="Select the AI model to chat with"
        )
        
        # Model information
        if config.show_model_info and selected_model:
            render_model_info(selected_model)
        
        # Session controls
        st.subheader("Session Controls")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ Clear Chat", help="Clear all messages"):
                st.session_state.clear_chat = True
        
        with col2:
            if st.button("🔄 New Session", help="Start a new session"):
                st.session_state.new_session = True
        
        # App information
        st.subheader("About")
        st.info(
            f"**{config.app_title}**\n\n"
            f"A production-grade chat application powered by Ollama.\n\n"
            f"**Features:**\n"
            f"• Multiple AI models\n"
            f"• Session management\n"
            f"• Message history\n"
            f"• Real-time streaming"
        )
        
        return selected_model


def render_model_info(model_name: str):
    """
    Render model information in the sidebar.
    
    Args:
        model_name: Name of the model to show info for
    """
    model_info = ollama_manager.get_model_info(model_name)
    
    if model_info:
        with st.expander("ℹ️ Model Info", expanded=False):
            st.write(f"**Name:** {model_info.name}")
            if model_info.size:
                st.write(f"**Size:** {model_info.size}")
            if model_info.family:
                st.write(f"**Family:** {model_info.family}")
            if model_info.parameter_size:
                st.write(f"**Parameters:** {model_info.parameter_size}")
            if model_info.modified:
                st.write(f"**Modified:** {model_info.modified}")


def render_chat_messages(session: ChatSession):
    """
    Render chat messages in the main area.
    
    Args:
        session: Current chat session
    """
    if not session.messages:
        st.info("👋 Start a conversation by typing a message below!")
        return
    
    # Display messages
    for message in session.messages:
        with st.chat_message(message.role):
            st.write(message.content)
            
            # Show timestamp for debugging (optional)
            if st.checkbox("Show timestamps", key=f"timestamp_{id(message)}"):
                st.caption(f"Sent at: {message.timestamp.strftime('%H:%M:%S')}")


def render_chat_input() -> Optional[str]:
    """
    Render the chat input area.
    
    Returns:
        User input message or None
    """
    # Chat input
    user_input = st.chat_input(
        "Type your message here...",
        max_chars=config.max_message_length
    )
    
    return user_input


def render_session_stats(session: Optional[ChatSession]):
    """
    Render session statistics.
    
    Args:
        session: Current chat session
    """
    if not session:
        return
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Messages", session.get_message_count())
    
    with col2:
        st.metric("Model", session.model_name.split(':')[0])
    
    with col3:
        duration = datetime.now() - session.created_at
        minutes = int(duration.total_seconds() / 60)
        st.metric("Session Time", f"{minutes}m")


def show_error_message(message: str):
    """
    Show an error message to the user.
    
    Args:
        message: Error message to display
    """
    st.error(f"❌ {message}")


def show_success_message(message: str):
    """
    Show a success message to the user.
    
    Args:
        message: Success message to display
    """
    st.success(f"✅ {message}")


def show_warning_message(message: str):
    """
    Show a warning message to the user.
    
    Args:
        message: Warning message to display
    """
    st.warning(f"⚠️ {message}")


def render_loading_spinner(text: str = "Processing..."):
    """
    Render a loading spinner with text.
    
    Args:
        text: Loading text to display
    """
    return st.spinner(text)


def render_web_content_manager():
    """
    Render the web content management interface.
    """
    st.subheader("📚 Knowledge Base")

    # URL input section
    st.write("**Add Web Content**")

    # Text area for multiple URLs
    urls_input = st.text_area(
        "Enter URLs (one per line):",
        height=100,
        placeholder="https://example.com/page1\nhttps://example.com/document.pdf\nhttps://another-site.com/article",
        help="Enter one URL per line. Supports both HTML pages and PDF documents."
    )

    col1, col2 = st.columns([1, 1])

    with col1:
        if st.button("📥 Download & Process", type="primary"):
            if urls_input.strip():
                urls = [url.strip() for url in urls_input.split('\n') if url.strip()]
                process_urls(urls)
            else:
                st.warning("Please enter at least one URL")

    with col2:
        if st.button("🗑️ Clear All Documents"):
            if st.session_state.get('confirm_clear', False):
                if knowledge_base.clear_all():
                    st.success("All documents cleared from knowledge base")
                    st.session_state.confirm_clear = False
                    st.rerun()
                else:
                    st.error("Failed to clear knowledge base")
            else:
                st.session_state.confirm_clear = True
                st.warning("Click again to confirm clearing all documents")

    # Knowledge base stats
    render_knowledge_base_stats()

    # Document list
    render_document_list()


def process_urls(urls: List[str]):
    """
    Process a list of URLs and add them to the knowledge base.

    Args:
        urls: List of URLs to process
    """
    with st.spinner("Processing URLs..."):
        results = web_downloader.process_urls(urls)

        # Show results
        if results['successful']:
            st.success(f"Successfully processed {len(results['successful'])} documents")

            # Add to knowledge base and collect chunk information
            added_count = 0
            processed_docs = []

            for doc in results['successful']:
                if knowledge_base.add_document(
                    title=doc['title'],
                    content=doc['content'],
                    url=doc['url'],
                    content_type=doc['content_type']
                ):
                    added_count += 1
                    processed_docs.append(doc)

            st.info(f"Added {added_count} documents to knowledge base")

            # Show chunks content for processed documents
            if processed_docs:
                st.subheader("📄 Document Chunks Preview")
                st.info("Below are the text chunks created from your documents. These chunks are what the AI will search through to answer your questions.")

                for doc in processed_docs:
                    with st.expander(f"📄 {doc['title']} - Chunks Preview"):
                        st.write(f"**Source:** {doc['url']}")
                        st.write(f"**Type:** {doc['content_type'].upper()}")

                        # Get chunks for this document by searching for content
                        try:
                            # Search for chunks from this specific document
                            search_results = knowledge_base.search_content(
                                doc['title'][:100],  # Use title as search query
                                n_results=10  # Get more chunks to show
                            )

                            # Filter results to only show chunks from this document
                            doc_chunks = [
                                result for result in search_results
                                if result['metadata'].get('url') == doc['url']
                            ]

                            if doc_chunks:
                                st.write(f"**Total Chunks:** {len(doc_chunks)}")

                                for i, chunk in enumerate(doc_chunks[:5], 1):  # Show first 5 chunks
                                    st.write(f"**Chunk {i}:**")
                                    st.text_area(
                                        f"Content",
                                        chunk['content'][:500] + ("..." if len(chunk['content']) > 500 else ""),
                                        height=100,
                                        key=f"chunk_{doc['url']}_{i}",
                                        disabled=True
                                    )

                                if len(doc_chunks) > 5:
                                    st.write(f"... and {len(doc_chunks) - 5} more chunks")
                            else:
                                st.write("No chunks found for this document")

                        except Exception as e:
                            st.error(f"Error retrieving chunks: {e}")

        if results['failed']:
            st.error(f"Failed to process {len(results['failed'])} URLs:")
            for failed in results['failed']:
                st.write(f"❌ {failed['url']}: {failed['error']}")

        if results['invalid_urls']:
            st.warning(f"Invalid URLs: {', '.join(results['invalid_urls'])}")


def render_knowledge_base_stats():
    """Render knowledge base statistics."""
    try:
        stats = knowledge_base.get_stats()

        if stats:
            st.write("**Knowledge Base Statistics**")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Documents", stats.get('total_documents', 0))

            with col2:
                st.metric("Text Chunks", stats.get('total_chunks', 0))

            with col3:
                avg_chunks = stats.get('avg_chunks_per_doc', 0)
                st.metric("Avg Chunks/Doc", f"{avg_chunks:.1f}")

            # Content types breakdown
            content_types = stats.get('content_types', {})
            if content_types:
                st.write("**Content Types:**")
                for content_type, count in content_types.items():
                    st.write(f"• {content_type.upper()}: {count} documents")

    except Exception as e:
        st.error(f"Error loading stats: {e}")


def render_document_list():
    """Render list of documents in the knowledge base."""
    try:
        documents = knowledge_base.get_all_documents()

        if documents:
            st.write("**Stored Documents**")

            for doc in documents:
                with st.expander(f"📄 {doc['title'][:50]}{'...' if len(doc['title']) > 50 else ''}"):
                    st.write(f"**URL:** {doc['url']}")
                    st.write(f"**Type:** {doc['content_type'].upper()}")
                    st.write(f"**Chunks:** {doc['chunk_count']}")
                    st.write(f"**Added:** {doc['added_at'][:19]}")

                    if st.button(f"🗑️ Delete", key=f"delete_{doc['url']}"):
                        if knowledge_base.delete_document(doc['url']):
                            st.success("Document deleted")
                            st.rerun()
                        else:
                            st.error("Failed to delete document")
        else:
            st.info("No documents in knowledge base. Add some URLs above to get started!")

    except Exception as e:
        st.error(f"Error loading documents: {e}")


def render_source_documents(source_docs: List[Dict]):
    """
    Render source documents used in the AI response.

    Args:
        source_docs: List of source documents with metadata and content
    """
    if not source_docs:
        return

    st.subheader("📚 Sources Used")
    st.info(f"The AI used these {len(source_docs)} documents to answer your question:")

    for i, doc in enumerate(source_docs, 1):
        with st.expander(f"📄 Source {i}: {doc['metadata'].get('title', 'Unknown Title')[:60]}..."):
            # Document metadata
            col1, col2 = st.columns(2)

            with col1:
                st.write(f"**URL:** {doc['metadata'].get('url', 'Unknown')}")
                st.write(f"**Type:** {doc['metadata'].get('content_type', 'unknown').upper()}")

            with col2:
                st.write(f"**Relevance Score:** {1 - doc.get('distance', 0):.3f}")
                st.write(f"**Chunk:** {doc['metadata'].get('chunk_index', 0) + 1} of {doc['metadata'].get('total_chunks', 1)}")

            # Document content
            st.write("**Content Used:**")
            st.text_area(
                "Content",
                doc['content'][:1000] + ("..." if len(doc['content']) > 1000 else ""),
                height=150,
                key=f"source_content_{i}",
                disabled=True
            )

            # Link to full document
            if doc['metadata'].get('url'):
                st.markdown(f"[🔗 View Full Document]({doc['metadata']['url']})")


def render_chat_sources_section():
    """Render the sources section below chat input."""
    if 'last_sources' in st.session_state and st.session_state.last_sources:
        render_source_documents(st.session_state.last_sources)
