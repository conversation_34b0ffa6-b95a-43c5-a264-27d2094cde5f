"""
Reusable UI components for the chat application.
"""

import streamlit as st
import logging
import json
from typing import List, Optional, Dict
from datetime import datetime

logger = logging.getLogger(__name__)

from .models import ollama_manager
from .chat_handler import ChatSession
from .web_content import web_downloader
from .knowledge_base import knowledge_base
from .utils.config import config


def render_sidebar() -> Optional[str]:
    """
    Render the sidebar with model selection and settings.
    
    Returns:
        Selected model name or None
    """
    with st.sidebar:
        st.title("🤖 Chat Settings")
        
        # Model selection
        st.subheader("Model Selection")
        available_models = ollama_manager.get_available_models()
        
        if not available_models:
            st.error("No models available. Please ensure Ollama is running.")
            return None
        
        # Set default model index
        default_index = 0
        if config.default_model in available_models:
            default_index = available_models.index(config.default_model)
        
        selected_model = st.selectbox(
            "Choose a model:",
            available_models,
            index=default_index,
            help="Select the AI model to chat with"
        )
        
        # Model information
        if config.show_model_info and selected_model:
            render_model_info(selected_model)
        
        # Session controls
        st.subheader("Session Controls")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ Clear Chat", help="Clear all messages"):
                st.session_state.clear_chat = True
        
        with col2:
            if st.button("🔄 New Session", help="Start a new session"):
                st.session_state.new_session = True
        
        # App information
        st.subheader("About")
        st.info(
            f"**{config.app_title}**\n\n"
            f"A production-grade chat application powered by Ollama.\n\n"
            f"**Features:**\n"
            f"• Multiple AI models\n"
            f"• Session management\n"
            f"• Message history\n"
            f"• Real-time streaming"
        )
        
        return selected_model


def render_model_info(model_name: str):
    """
    Render model information in the sidebar.
    
    Args:
        model_name: Name of the model to show info for
    """
    model_info = ollama_manager.get_model_info(model_name)
    
    if model_info:
        with st.expander("ℹ️ Model Info", expanded=False):
            st.write(f"**Name:** {model_info.name}")
            if model_info.size:
                st.write(f"**Size:** {model_info.size}")
            if model_info.family:
                st.write(f"**Family:** {model_info.family}")
            if model_info.parameter_size:
                st.write(f"**Parameters:** {model_info.parameter_size}")
            if model_info.modified:
                st.write(f"**Modified:** {model_info.modified}")


def render_chat_messages(session: ChatSession):
    """
    Render chat messages in the main area.
    
    Args:
        session: Current chat session
    """
    if not session.messages:
        st.info("👋 Start a conversation by typing a message below!")
        return
    
    # Display messages
    for message in session.messages:
        with st.chat_message(message.role):
            st.write(message.content)
            
            # Show timestamp for debugging (optional)
            if st.checkbox("Show timestamps", key=f"timestamp_{id(message)}"):
                st.caption(f"Sent at: {message.timestamp.strftime('%H:%M:%S')}")


def render_chat_input() -> Optional[str]:
    """
    Render the chat input area.
    
    Returns:
        User input message or None
    """
    # Chat input
    user_input = st.chat_input(
        "Type your message here...",
        max_chars=config.max_message_length
    )
    
    return user_input


def render_session_stats(session: Optional[ChatSession]):
    """
    Render session statistics.
    
    Args:
        session: Current chat session
    """
    if not session:
        return
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Messages", session.get_message_count())
    
    with col2:
        st.metric("Model", session.model_name.split(':')[0])
    
    with col3:
        duration = datetime.now() - session.created_at
        minutes = int(duration.total_seconds() / 60)
        st.metric("Session Time", f"{minutes}m")


def show_error_message(message: str):
    """
    Show an error message to the user.
    
    Args:
        message: Error message to display
    """
    st.error(f"❌ {message}")


def show_success_message(message: str):
    """
    Show a success message to the user.
    
    Args:
        message: Success message to display
    """
    st.success(f"✅ {message}")


def show_warning_message(message: str):
    """
    Show a warning message to the user.
    
    Args:
        message: Warning message to display
    """
    st.warning(f"⚠️ {message}")


def render_loading_spinner(text: str = "Processing..."):
    """
    Render a loading spinner with text.
    
    Args:
        text: Loading text to display
    """
    return st.spinner(text)


def render_web_content_manager():
    """
    Render the web content management interface.
    """
    st.subheader("📚 Knowledge Base")

    # URL input section
    st.write("**Add Web Content**")

    # Text area for multiple URLs
    urls_input = st.text_area(
        "Enter URLs (one per line):",
        height=100,
        placeholder="https://example.com/page1\nhttps://example.com/document.pdf\nhttps://another-site.com/article",
        help="Enter one URL per line. Supports both HTML pages and PDF documents."
    )

    col1, col2 = st.columns([1, 1])

    with col1:
        if st.button("📥 Download & Process", type="primary"):
            if urls_input.strip():
                urls = [url.strip() for url in urls_input.split('\n') if url.strip()]
                process_urls(urls)
            else:
                st.warning("Please enter at least one URL")

    with col2:
        if st.button("🗑️ Clear All Documents"):
            if st.session_state.get('confirm_clear', False):
                if knowledge_base.clear_all():
                    st.success("All documents cleared from knowledge base")
                    st.session_state.confirm_clear = False
                    st.rerun()
                else:
                    st.error("Failed to clear knowledge base")
            else:
                st.session_state.confirm_clear = True
                st.warning("Click again to confirm clearing all documents")

    # Knowledge base stats
    render_knowledge_base_stats()

    # Document list
    render_document_list()


def process_urls(urls: List[str]):
    """
    Process a list of URLs and add them to the knowledge base.

    Args:
        urls: List of URLs to process
    """
    with st.spinner("Processing URLs..."):
        results = web_downloader.process_urls(urls)

        # Show results
        if results['successful']:
            st.success(f"Successfully processed {len(results['successful'])} documents")

            # Add to knowledge base and collect chunk information
            added_count = 0
            processed_docs = []

            for doc in results['successful']:
                if knowledge_base.add_document(
                    title=doc['title'],
                    content=doc['content'],
                    url=doc['url'],
                    content_type=doc['content_type']
                ):
                    added_count += 1
                    processed_docs.append(doc)

            st.info(f"Added {added_count} documents to knowledge base")

            # Show chunks content for processed documents
            if processed_docs:
                st.subheader("📄 Document Chunks Preview")
                st.info("Below are the text chunks created from your documents. These chunks are what the AI will search through to answer your questions.")

                for doc in processed_docs:
                    with st.expander(f"📄 {doc['title']} - Chunks Preview"):
                        st.write(f"**Source:** {doc['url']}")
                        st.write(f"**Type:** {doc['content_type'].upper()}")

                        # Get chunks for this document by searching for content
                        try:
                            # Search for chunks from this specific document
                            search_results = knowledge_base.search_content(
                                doc['title'][:100],  # Use title as search query
                                n_results=10  # Get more chunks to show
                            )

                            # Filter results to only show chunks from this document
                            doc_chunks = [
                                result for result in search_results
                                if result['metadata'].get('url') == doc['url']
                            ]

                            if doc_chunks:
                                st.write(f"**Total Chunks:** {len(doc_chunks)}")

                                for i, chunk in enumerate(doc_chunks[:5], 1):  # Show first 5 chunks
                                    st.write(f"**Chunk {i}:**")
                                    st.text_area(
                                        f"Content",
                                        chunk['content'][:500] + ("..." if len(chunk['content']) > 500 else ""),
                                        height=100,
                                        key=f"chunk_{doc['url']}_{i}",
                                        disabled=True
                                    )

                                if len(doc_chunks) > 5:
                                    st.write(f"... and {len(doc_chunks) - 5} more chunks")
                            else:
                                st.write("No chunks found for this document")

                        except Exception as e:
                            st.error(f"Error retrieving chunks: {e}")

        if results['failed']:
            st.error(f"Failed to process {len(results['failed'])} URLs:")
            for failed in results['failed']:
                st.write(f"❌ {failed['url']}: {failed['error']}")

        if results['invalid_urls']:
            st.warning(f"Invalid URLs: {', '.join(results['invalid_urls'])}")


def render_knowledge_base_stats():
    """Render knowledge base statistics."""
    try:
        stats = knowledge_base.get_stats()

        if stats:
            st.write("**Knowledge Base Statistics**")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Documents", stats.get('total_documents', 0))

            with col2:
                st.metric("Text Chunks", stats.get('total_chunks', 0))

            with col3:
                avg_chunks = stats.get('avg_chunks_per_doc', 0)
                st.metric("Avg Chunks/Doc", f"{avg_chunks:.1f}")

            # Content types breakdown
            content_types = stats.get('content_types', {})
            if content_types:
                st.write("**Content Types:**")
                for content_type, count in content_types.items():
                    st.write(f"• {content_type.upper()}: {count} documents")

    except Exception as e:
        st.error(f"Error loading stats: {e}")


def render_document_list():
    """Render list of documents in the knowledge base."""
    try:
        documents = knowledge_base.get_all_documents()

        if documents:
            st.write("**Stored Documents**")

            for doc in documents:
                with st.expander(f"📄 {doc['title'][:50]}{'...' if len(doc['title']) > 50 else ''}"):
                    st.write(f"**URL:** {doc['url']}")
                    st.write(f"**Type:** {doc['content_type'].upper()}")
                    st.write(f"**Chunks:** {doc['chunk_count']}")
                    st.write(f"**Added:** {doc['added_at'][:19]}")

                    if st.button(f"🗑️ Delete", key=f"delete_{doc['url']}"):
                        if knowledge_base.delete_document(doc['url']):
                            st.success("Document deleted")
                            st.rerun()
                        else:
                            st.error("Failed to delete document")
        else:
            st.info("No documents in knowledge base. Add some URLs above to get started!")

    except Exception as e:
        st.error(f"Error loading documents: {e}")


def render_source_documents(source_docs: List[Dict]):
    """
    Render source documents used in the AI response.

    Args:
        source_docs: List of source documents with metadata and content
    """
    if not source_docs:
        return

    st.subheader("📚 Sources Used")
    st.info(f"The AI used these {len(source_docs)} documents to answer your question:")

    for i, doc in enumerate(source_docs, 1):
        with st.expander(f"📄 Source {i}: {doc['metadata'].get('title', 'Unknown Title')[:60]}..."):
            # Document metadata
            col1, col2 = st.columns(2)

            with col1:
                st.write(f"**URL:** {doc['metadata'].get('url', 'Unknown')}")
                st.write(f"**Type:** {doc['metadata'].get('content_type', 'unknown').upper()}")

            with col2:
                st.write(f"**Relevance Score:** {1 - doc.get('distance', 0):.3f}")
                st.write(f"**Chunk:** {doc['metadata'].get('chunk_index', 0) + 1} of {doc['metadata'].get('total_chunks', 1)}")

            # Document content
            st.write("**Content Used:**")
            st.text_area(
                "Content",
                doc['content'][:1000] + ("..." if len(doc['content']) > 1000 else ""),
                height=150,
                key=f"source_content_{i}",
                disabled=True
            )

            # Link to full document
            if doc['metadata'].get('url'):
                st.markdown(f"[🔗 View Full Document]({doc['metadata']['url']})")


def render_chat_sources_section():
    """Render the sources section below chat input."""
    if 'last_sources' in st.session_state and st.session_state.last_sources:
        render_source_documents(st.session_state.last_sources)


def render_all_documents_manager():
    """
    Render the comprehensive document management interface.
    """
    st.subheader("📋 All Documents")
    st.info("Comprehensive view and management of all documents in your knowledge base.")

    try:
        # Get all documents and stats
        documents = knowledge_base.get_all_documents()
        stats = knowledge_base.get_stats()

        # Header with stats
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("📄 Total Documents", stats.get('total_documents', 0))

        with col2:
            st.metric("🧩 Total Chunks", stats.get('total_chunks', 0))

        with col3:
            avg_chunks = stats.get('avg_chunks_per_doc', 0)
            st.metric("📊 Avg Chunks/Doc", f"{avg_chunks:.1f}")

        with col4:
            # Calculate total storage estimate
            total_chunks = stats.get('total_chunks', 0)
            storage_mb = (total_chunks * 3) / 1024  # Rough estimate: 3KB per chunk
            st.metric("💾 Est. Storage", f"{storage_mb:.1f} MB")

        # Content type breakdown
        content_types = stats.get('content_types', {})
        if content_types:
            st.write("**📊 Content Types Distribution:**")
            cols = st.columns(len(content_types))
            for i, (content_type, count) in enumerate(content_types.items()):
                with cols[i]:
                    st.metric(f"{content_type.upper()}", count)

        st.divider()

        # Search and filter options
        st.write("**🔍 Search & Filter Documents**")

        col1, col2 = st.columns([3, 1])

        with col1:
            search_query = st.text_input(
                "Search documents by title or URL:",
                placeholder="Enter search terms...",
                help="Search through document titles and URLs"
            )

        with col2:
            filter_type = st.selectbox(
                "Filter by type:",
                ["All", "HTML", "PDF"],
                help="Filter documents by content type"
            )

        # Search in Chunks section
        st.write("**🔍 Search in Chunks**")
        st.info("Search for specific content within all document chunks in your knowledge base.")

        col1, col2, col3 = st.columns([4, 1, 1])

        with col1:
            chunk_search_query = st.text_input(
                "Search within document content:",
                placeholder="Enter text to search for in chunks...",
                help="Search for specific content within all document chunks",
                key="chunk_search_input"
            )

        with col2:
            max_results = st.selectbox(
                "Max results:",
                [5, 10, 20, 50],
                index=1,
                help="Maximum number of chunks to return"
            )

        with col3:
            if st.button("🔍 Search Chunks", help="Search for content in all chunks"):
                if chunk_search_query:
                    render_chunk_search_results(chunk_search_query, max_results)
                else:
                    st.warning("Please enter a search query.")

        # Show chunk search results if available
        if 'chunk_search_results' in st.session_state and st.session_state.chunk_search_results:
            render_chunk_search_results_display()

        # Bulk actions
        st.write("**⚡ Bulk Actions**")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 Refresh List", help="Reload document list"):
                st.rerun()

        with col2:
            if st.button("📊 Export List", help="Export document list as JSON"):
                export_documents_list(documents)

        with col3:
            if st.button("🗑️ Clear All", help="Delete all documents", type="secondary"):
                handle_clear_all_documents()

        st.divider()

        # Filter and display documents
        filtered_docs = filter_documents(documents, search_query, filter_type)

        if filtered_docs:
            st.write(f"**📚 Documents ({len(filtered_docs)} found)**")

            # Sort options
            sort_col1, sort_col2 = st.columns(2)
            with sort_col1:
                sort_by = st.selectbox(
                    "Sort by:",
                    ["Added Date (Newest)", "Added Date (Oldest)", "Title (A-Z)", "Title (Z-A)", "Chunks (Most)", "Chunks (Least)"]
                )

            with sort_col2:
                view_mode = st.radio(
                    "View mode:",
                    ["Detailed", "Compact"],
                    horizontal=True
                )

            # Sort documents
            sorted_docs = sort_documents(filtered_docs, sort_by)

            # Display documents
            if view_mode == "Detailed":
                render_detailed_document_view(sorted_docs)
            else:
                render_compact_document_view(sorted_docs)

        else:
            if search_query or filter_type != "All":
                st.warning("🔍 No documents found matching your search criteria.")
            else:
                st.info("📭 No documents in knowledge base. Add some URLs in the Knowledge Base tab to get started!")

    except Exception as e:
        st.error(f"❌ Error loading documents: {e}")
        logger.error(f"Error in render_all_documents_manager: {e}")


def filter_documents(documents: List[Dict], search_query: str, filter_type: str) -> List[Dict]:
    """
    Filter documents based on search query and content type.

    Args:
        documents: List of document dictionaries
        search_query: Search term for title/URL
        filter_type: Content type filter ("All", "HTML", "PDF")

    Returns:
        Filtered list of documents
    """
    filtered = documents

    # Filter by search query
    if search_query:
        search_lower = search_query.lower()
        filtered = [
            doc for doc in filtered
            if (search_lower in doc.get('title', '').lower() or
                search_lower in doc.get('url', '').lower())
        ]

    # Filter by content type
    if filter_type != "All":
        filtered = [
            doc for doc in filtered
            if doc.get('content_type', '').upper() == filter_type.upper()
        ]

    return filtered


def sort_documents(documents: List[Dict], sort_by: str) -> List[Dict]:
    """
    Sort documents based on the specified criteria.

    Args:
        documents: List of document dictionaries
        sort_by: Sort criteria

    Returns:
        Sorted list of documents
    """
    if sort_by == "Added Date (Newest)":
        return sorted(documents, key=lambda x: x.get('added_at', ''), reverse=True)
    elif sort_by == "Added Date (Oldest)":
        return sorted(documents, key=lambda x: x.get('added_at', ''))
    elif sort_by == "Title (A-Z)":
        return sorted(documents, key=lambda x: x.get('title', '').lower())
    elif sort_by == "Title (Z-A)":
        return sorted(documents, key=lambda x: x.get('title', '').lower(), reverse=True)
    elif sort_by == "Chunks (Most)":
        return sorted(documents, key=lambda x: x.get('chunk_count', 0), reverse=True)
    elif sort_by == "Chunks (Least)":
        return sorted(documents, key=lambda x: x.get('chunk_count', 0))
    else:
        return documents


def export_documents_list(documents: List[Dict]):
    """
    Export documents list as JSON for download.

    Args:
        documents: List of document dictionaries
    """
    try:
        # Prepare export data
        export_data = {
            "export_date": datetime.now().isoformat(),
            "total_documents": len(documents),
            "documents": documents
        }

        # Convert to JSON
        json_str = json.dumps(export_data, indent=2, ensure_ascii=False)

        # Provide download
        st.download_button(
            label="📥 Download JSON",
            data=json_str,
            file_name=f"knowledge_base_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json",
            help="Download complete document list as JSON file"
        )

        st.success("✅ Export ready! Click the download button above.")

    except Exception as e:
        st.error(f"❌ Error exporting documents: {e}")
        logger.error(f"Error in export_documents_list: {e}")


def handle_clear_all_documents():
    """
    Handle clearing all documents with confirmation.
    """
    if 'confirm_clear_all' not in st.session_state:
        st.session_state.confirm_clear_all = False

    if not st.session_state.confirm_clear_all:
        st.warning("⚠️ **Are you sure you want to delete ALL documents?**")
        st.write("This action cannot be undone. All documents and their chunks will be permanently removed.")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("✅ Yes, Delete All", type="primary"):
                st.session_state.confirm_clear_all = True
                st.rerun()

        with col2:
            if st.button("❌ Cancel"):
                st.info("Operation cancelled.")
    else:
        try:
            with st.spinner("Clearing all documents..."):
                if knowledge_base.clear_all():
                    st.success("✅ All documents have been deleted successfully!")
                    st.session_state.confirm_clear_all = False
                    st.rerun()
                else:
                    st.error("❌ Failed to clear documents. Please try again.")
                    st.session_state.confirm_clear_all = False
        except Exception as e:
            st.error(f"❌ Error clearing documents: {e}")
            logger.error(f"Error in handle_clear_all_documents: {e}")
            st.session_state.confirm_clear_all = False


def render_detailed_document_view(documents: List[Dict]):
    """
    Render documents in detailed view with full information.

    Args:
        documents: List of document dictionaries
    """
    for i, doc in enumerate(documents, 1):
        # Document header (not in expander to avoid nesting issues)
        st.markdown(f"### 📄 {i}. {doc.get('title', 'Unknown Title')}")

        # Document metadata
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.write(f"**🔗 URL:** {doc.get('url', 'Unknown')}")
            st.write(f"**📅 Added:** {doc.get('added_at', 'Unknown')}")

        with col2:
            st.write(f"**📊 Type:** {doc.get('content_type', 'unknown').upper()}")
            st.write(f"**🧩 Chunks:** {doc.get('chunk_count', 0)}")

        with col3:
            # Action buttons
            if st.button(f"🗑️ Delete Document", key=f"delete_doc_{i}", help="Delete this entire document"):
                handle_delete_document(doc.get('url'))

        # Document chunks - show all chunks with full content
        st.write("**📖 All Document Chunks:**")
        try:
            # Get ALL chunks from this document by searching with a broad query
            # and then filtering by URL
            all_search_results = knowledge_base.search_content(
                "",  # Empty query to get all chunks
                n_results=1000  # Large number to get all chunks
            )

            # Filter to only this document's chunks
            doc_chunks = [
                result for result in all_search_results
                if result['metadata'].get('url') == doc.get('url')
            ]

            if doc_chunks:
                st.write(f"**Total Chunks:** {len(doc_chunks)}")

                # Sort chunks by chunk_index if available
                try:
                    doc_chunks.sort(key=lambda x: x['metadata'].get('chunk_index', 0))
                except:
                    pass  # If sorting fails, keep original order

                # Display all chunks using expanders (no nesting now)
                for j, chunk in enumerate(doc_chunks, 1):
                    chunk_title = f"Chunk {j}/{len(doc_chunks)}"
                    if 'chunk_index' in chunk['metadata']:
                        chunk_title = f"Chunk {chunk['metadata']['chunk_index'] + 1}/{chunk['metadata'].get('total_chunks', len(doc_chunks))}"

                    # Create a unique key for this chunk
                    chunk_key = f"chunk_{doc.get('url', '')}_{j}"
                    chunk_id = chunk.get('id', f"unknown_{j}")

                    # Use proper expander for each chunk (no nesting issues now)
                    with st.expander(f"📄 {chunk_title}", expanded=False):
                        # Chunk metadata and actions
                        col1, col2 = st.columns([3, 1])

                        with col1:
                            st.write(f"**Chunk ID:** {chunk_id}")
                            st.write(f"**Length:** {len(chunk['content'])} characters")

                        with col2:
                            # Delete chunk button
                            if st.button(f"🗑️ Delete", key=f"delete_{chunk_key}", help="Delete this chunk"):
                                handle_delete_chunk(chunk, chunk_id)

                        # Full chunk content
                        st.markdown("**📖 Full Content:**")
                        st.text_area(
                            "Content:",
                            chunk['content'],
                            height=200,
                            key=f"content_{chunk_key}",
                            disabled=True
                        )

                        # Chunk statistics
                        word_count = len(chunk['content'].split())
                        line_count = len(chunk['content'].split('\n'))
                        st.caption(f"📊 {word_count} words, {line_count} lines")
            else:
                st.write("No chunks found for this document")

        except Exception as e:
            st.error(f"Error loading chunks: {e}")
            logger.error(f"Error loading chunks for document {doc.get('url')}: {e}")

        # Quick actions
        st.write("**🔧 Document Actions:**")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button(f"🔍 Search in Doc", key=f"search_{i}"):
                st.session_state[f"search_doc_{i}"] = True

        with col2:
            if doc.get('url'):
                st.markdown(f"[🌐 Open Original]({doc['url']})")

        with col3:
            if st.button(f"📋 Copy URL", key=f"copy_{i}"):
                st.code(doc.get('url', ''), language=None)

        # Search within document
        if st.session_state.get(f"search_doc_{i}", False):
            st.write("**🔍 Search within this document:**")
            search_in_doc = st.text_input(
                f"Search terms:",
                key=f"search_input_{i}",
                placeholder="Enter search terms..."
            )

            if search_in_doc:
                try:
                    # Search for content within this specific document
                    results = knowledge_base.search_content(search_in_doc, n_results=10)
                    doc_results = [
                        r for r in results
                        if r['metadata'].get('url') == doc.get('url')
                    ]

                    if doc_results:
                        st.write(f"Found {len(doc_results)} matching chunks:")
                        for j, result in enumerate(doc_results[:3], 1):
                            st.write(f"**Match {j}:**")
                            st.text_area(
                                f"Content",
                                result['content'][:300] + ("..." if len(result['content']) > 300 else ""),
                                height=80,
                                key=f"match_{i}_{j}",
                                disabled=True
                            )
                    else:
                        st.write("No matches found in this document.")
                except Exception as e:
                    st.error(f"Search error: {e}")

        # Add separator between documents
        st.markdown("---")


def render_compact_document_view(documents: List[Dict]):
    """
    Render documents in compact table view.

    Args:
        documents: List of document dictionaries
    """
    if not documents:
        return

    # Create table data
    table_data = []
    for i, doc in enumerate(documents, 1):
        table_data.append({
            "#": i,
            "Title": doc.get('title', 'Unknown')[:50] + ("..." if len(doc.get('title', '')) > 50 else ""),
            "Type": doc.get('content_type', 'unknown').upper(),
            "Chunks": doc.get('chunk_count', 0),
            "Added": doc.get('added_at', 'Unknown')[:10] if doc.get('added_at') else 'Unknown',
            "URL": doc.get('url', 'Unknown')
        })

    # Display table
    st.dataframe(
        table_data,
        use_container_width=True,
        hide_index=True,
        column_config={
            "#": st.column_config.NumberColumn("No.", width="small"),
            "Title": st.column_config.TextColumn("Document Title", width="large"),
            "Type": st.column_config.TextColumn("Type", width="small"),
            "Chunks": st.column_config.NumberColumn("Chunks", width="small"),
            "Added": st.column_config.TextColumn("Added", width="medium"),
            "URL": st.column_config.LinkColumn("Source URL", width="large")
        }
    )

    # Bulk selection for actions
    st.write("**🔧 Bulk Actions:**")

    # Document selection
    selected_indices = st.multiselect(
        "Select documents for bulk actions:",
        options=list(range(len(documents))),
        format_func=lambda x: f"{x+1}. {documents[x].get('title', 'Unknown')[:50]}...",
        help="Select multiple documents to perform bulk actions"
    )

    if selected_indices:
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button(f"🗑️ Delete Selected ({len(selected_indices)})", type="secondary"):
                handle_bulk_delete_documents([documents[i] for i in selected_indices])

        with col2:
            if st.button(f"📊 Export Selected ({len(selected_indices)})"):
                export_documents_list([documents[i] for i in selected_indices])

        with col3:
            selected_urls = [documents[i].get('url') for i in selected_indices]
            urls_text = "\n".join(selected_urls)
            st.download_button(
                f"📋 Copy URLs ({len(selected_indices)})",
                data=urls_text,
                file_name="selected_urls.txt",
                mime="text/plain"
            )


def handle_delete_document(url: str):
    """
    Handle deleting a single document with confirmation.

    Args:
        url: URL of the document to delete
    """
    # Create a unique key for this document's confirmation state
    confirm_key = f"confirm_delete_{hash(url)}"

    if confirm_key not in st.session_state:
        st.session_state[confirm_key] = False

    if not st.session_state[confirm_key]:
        # Show confirmation dialog
        st.warning(f"⚠️ **Delete this document?**")
        st.write(f"URL: {url}")
        st.write("This action cannot be undone.")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("✅ Yes, Delete", key=f"confirm_yes_{hash(url)}", type="primary"):
                st.session_state[confirm_key] = True
                st.rerun()

        with col2:
            if st.button("❌ Cancel", key=f"confirm_no_{hash(url)}"):
                st.info("Delete cancelled.")
                return
    else:
        # Perform the deletion
        try:
            with st.spinner("Deleting document..."):
                if knowledge_base.delete_document(url):
                    st.success(f"✅ Document deleted successfully!")
                    st.session_state[confirm_key] = False
                    st.rerun()
                else:
                    st.error("❌ Failed to delete document. It may not exist in the knowledge base.")
                    st.session_state[confirm_key] = False
        except Exception as e:
            st.error(f"❌ Error deleting document: {e}")
            logger.error(f"Error deleting document {url}: {e}")
            st.session_state[confirm_key] = False


def handle_bulk_delete_documents(documents: List[Dict]):
    """
    Handle deleting multiple documents with confirmation.

    Args:
        documents: List of documents to delete
    """
    # Create unique key based on document URLs
    doc_urls = [doc.get('url', '') for doc in documents]
    confirm_key = f"confirm_bulk_delete_{hash(str(doc_urls))}"

    if confirm_key not in st.session_state:
        st.session_state[confirm_key] = False

    if not st.session_state[confirm_key]:
        st.warning(f"⚠️ **Delete {len(documents)} selected documents?**")
        st.write("This action cannot be undone.")

        # Show list of documents to be deleted
        with st.expander("📋 Documents to be deleted:"):
            for i, doc in enumerate(documents, 1):
                st.write(f"{i}. {doc.get('title', 'Unknown')[:60]}...")
                st.write(f"   URL: {doc.get('url', 'Unknown')}")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("✅ Yes, Delete All", key=f"bulk_yes_{hash(str(doc_urls))}", type="primary"):
                st.session_state[confirm_key] = True
                st.rerun()

        with col2:
            if st.button("❌ Cancel", key=f"bulk_no_{hash(str(doc_urls))}"):
                st.info("Bulk delete cancelled.")
                return
    else:
        # Perform the bulk deletion
        try:
            with st.spinner(f"Deleting {len(documents)} documents..."):
                deleted_count = 0
                failed_count = 0

                progress_bar = st.progress(0)
                status_text = st.empty()

                for i, doc in enumerate(documents):
                    url = doc.get('url')
                    status_text.text(f"Deleting document {i+1}/{len(documents)}: {doc.get('title', 'Unknown')[:40]}...")

                    if knowledge_base.delete_document(url):
                        deleted_count += 1
                    else:
                        failed_count += 1

                    progress_bar.progress((i + 1) / len(documents))

                progress_bar.empty()
                status_text.empty()

                if deleted_count > 0:
                    st.success(f"✅ Successfully deleted {deleted_count} documents!")

                if failed_count > 0:
                    st.warning(f"⚠️ Failed to delete {failed_count} documents.")

                st.session_state[confirm_key] = False
                st.rerun()

        except Exception as e:
            st.error(f"❌ Error during bulk delete: {e}")
            logger.error(f"Error in bulk delete: {e}")
            st.session_state[confirm_key] = False


def render_chunk_search_results(query: str, max_results: int):
    """
    Perform chunk search and store results in session state.

    Args:
        query: Search query
        max_results: Maximum number of results to return
    """
    try:
        with st.spinner(f"Searching for '{query}' in all chunks..."):
            # Search in knowledge base
            search_results = knowledge_base.search_content(query, n_results=max_results)

            if search_results:
                st.session_state.chunk_search_results = {
                    'query': query,
                    'results': search_results,
                    'total_found': len(search_results)
                }
                st.success(f"✅ Found {len(search_results)} matching chunks!")
                st.rerun()
            else:
                st.warning(f"No chunks found containing '{query}'")
                st.session_state.chunk_search_results = None

    except Exception as e:
        st.error(f"❌ Error searching chunks: {e}")
        logger.error(f"Error in chunk search: {e}")


def render_chunk_search_results_display():
    """
    Display chunk search results from session state.
    """
    if 'chunk_search_results' not in st.session_state or not st.session_state.chunk_search_results:
        return

    results_data = st.session_state.chunk_search_results
    query = results_data['query']
    results = results_data['results']

    st.subheader(f"🔍 Search Results for: '{query}'")
    st.write(f"Found **{len(results)}** matching chunks:")

    # Clear results button
    if st.button("❌ Clear Search Results"):
        st.session_state.chunk_search_results = None
        st.rerun()

    # Display results
    for i, result in enumerate(results, 1):
        with st.expander(f"📄 Result {i}: {result['metadata'].get('title', 'Unknown')[:60]}... (Score: {1-result.get('distance', 0):.3f})"):
            # Metadata
            col1, col2 = st.columns(2)

            with col1:
                st.write(f"**Document:** {result['metadata'].get('title', 'Unknown')}")
                st.write(f"**URL:** {result['metadata'].get('url', 'Unknown')}")

            with col2:
                st.write(f"**Type:** {result['metadata'].get('content_type', 'unknown').upper()}")
                st.write(f"**Chunk:** {result['metadata'].get('chunk_index', 0) + 1} of {result['metadata'].get('total_chunks', 1)}")

            # Content with highlighting
            content = result['content']
            st.write("**Content:**")

            # Simple highlighting by making search terms bold
            highlighted_content = content
            for word in query.split():
                if len(word) > 2:  # Only highlight words longer than 2 characters
                    highlighted_content = highlighted_content.replace(
                        word, f"**{word}**"
                    ).replace(
                        word.lower(), f"**{word.lower()}**"
                    ).replace(
                        word.upper(), f"**{word.upper()}**"
                    ).replace(
                        word.capitalize(), f"**{word.capitalize()}**"
                    )

            st.markdown(highlighted_content)

            # Actions
            col1, col2 = st.columns(2)
            with col1:
                if result['metadata'].get('url'):
                    st.markdown(f"[🌐 View Source]({result['metadata']['url']})")

            with col2:
                if st.button(f"🗑️ Delete This Chunk", key=f"delete_chunk_search_{i}"):
                    handle_delete_chunk(result, result.get('id'))


def handle_delete_chunk(chunk_result: Dict, chunk_id: str = None):
    """
    Handle deleting a single chunk with confirmation.

    Args:
        chunk_result: The chunk result from search containing metadata and content
        chunk_id: Optional explicit chunk ID to use
    """
    # Use provided chunk_id or extract from chunk_result
    if chunk_id is None:
        chunk_id = chunk_result.get('id')

    # If still no chunk_id, we can't delete
    if not chunk_id or chunk_id == 'Unknown' or chunk_id.startswith('unknown_'):
        st.error("❌ Cannot delete chunk: No valid chunk ID found.")
        logger.error(f"Attempted to delete chunk without valid ID: {chunk_id}")
        return

    # Create a unique key for this chunk's confirmation state
    confirm_key = f"confirm_delete_chunk_{hash(chunk_id)}"

    if confirm_key not in st.session_state:
        st.session_state[confirm_key] = False

    if not st.session_state[confirm_key]:
        # Show confirmation dialog
        st.warning(f"⚠️ **Delete this chunk?**")
        st.write(f"**Chunk ID:** {chunk_id}")
        st.write(f"**From:** {chunk_result['metadata'].get('title', 'Unknown')}")
        st.write(f"**Content preview:** {chunk_result['content'][:100]}...")
        st.write("This action cannot be undone.")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("✅ Yes, Delete", key=f"confirm_chunk_yes_{hash(chunk_id)}", type="primary"):
                st.session_state[confirm_key] = True
                st.rerun()

        with col2:
            if st.button("❌ Cancel", key=f"confirm_chunk_no_{hash(chunk_id)}"):
                st.info("Delete cancelled.")
                st.session_state[confirm_key] = False
                return
    else:
        # Perform the deletion
        try:
            with st.spinner(f"Deleting chunk {chunk_id}..."):
                if knowledge_base.delete_chunk(chunk_id):
                    st.success(f"✅ Chunk {chunk_id} deleted successfully!")
                    st.session_state[confirm_key] = False
                    # Clear search results to refresh
                    if 'chunk_search_results' in st.session_state:
                        st.session_state.chunk_search_results = None
                    st.rerun()
                else:
                    st.error(f"❌ Failed to delete chunk {chunk_id}. It may not exist in the knowledge base.")
                    st.session_state[confirm_key] = False
        except Exception as e:
            st.error(f"❌ Error deleting chunk {chunk_id}: {e}")
            logger.error(f"Error deleting chunk {chunk_id}: {e}")
            st.session_state[confirm_key] = False
