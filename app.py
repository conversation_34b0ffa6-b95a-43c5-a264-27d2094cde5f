"""
Main Streamlit application for the Ollama Chat Assistant.

A production-grade chat application with multiple AI models,
session management, and real-time streaming responses.
"""

import logging
import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

import streamlit as st
from chat_app.chat_handler import chat_handler
from chat_app.knowledge_base import knowledge_base
from chat_app.ui_components import (
    render_sidebar,
    render_chat_messages,
    render_chat_input,
    render_session_stats,
    render_web_content_manager,
    render_all_documents_manager,
    show_error_message,
    show_success_message,
    render_chat_sources_section
)
from chat_app.utils.config import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "initialized" not in st.session_state:
        st.session_state.initialized = True
        st.session_state.current_model = None
        st.session_state.clear_chat = False
        st.session_state.new_session = False
        logger.info("Session state initialized")


def handle_model_change(selected_model: str):
    """
    Handle model selection change.
    
    Args:
        selected_model: The newly selected model
    """
    if st.session_state.current_model != selected_model:
        logger.info(f"Model changed from {st.session_state.current_model} to {selected_model}")
        
        # Start new session with the selected model
        if chat_handler.start_new_session(selected_model):
            st.session_state.current_model = selected_model
            show_success_message(f"Started new session with {selected_model}")
        else:
            show_error_message(f"Failed to start session with {selected_model}")
            return False
    
    return True


def handle_session_controls():
    """Handle session control actions (clear, new session)."""
    if st.session_state.get("clear_chat", False):
        chat_handler.clear_session()
        show_success_message("Chat history cleared")
        st.session_state.clear_chat = False
        st.rerun()
    
    if st.session_state.get("new_session", False):
        if st.session_state.current_model:
            if chat_handler.start_new_session(st.session_state.current_model):
                show_success_message("New session started")
            else:
                show_error_message("Failed to start new session")
        st.session_state.new_session = False
        st.rerun()


def handle_user_input(user_input: str):
    """
    Handle user message input and generate streaming AI response.

    Args:
        user_input: The user's message
    """
    current_session = chat_handler.get_current_session()

    if not current_session:
        show_error_message("No active session. Please select a model first.")
        return

    # Display user message immediately
    with st.chat_message("user"):
        st.write(user_input)

    # Generate and display streaming AI response
    with st.chat_message("assistant"):
        message_placeholder = st.empty()

        try:
            # Initialize response text
            response_text = ""

            # Show initial thinking indicator
            message_placeholder.write("🤔 Thinking...")

            # Stream the response
            first_chunk = True
            for chunk in chat_handler.send_message_streaming(user_input):
                if chunk:
                    # Clear thinking indicator on first chunk
                    if first_chunk:
                        response_text = chunk
                        first_chunk = False
                    else:
                        response_text += chunk

                    # Update the placeholder with accumulated text and cursor
                    message_placeholder.write(response_text + "▋")

            # Remove cursor when streaming is complete
            if response_text:
                message_placeholder.write(response_text)

            # Get sources after streaming is complete
            # We need to search again to get the sources for display
            try:
                search_results = knowledge_base.search_content(user_input, n_results=5)
                st.session_state.last_sources = search_results if search_results else []
            except Exception as e:
                logger.warning(f"Could not retrieve sources for display: {e}")
                st.session_state.last_sources = []

        except Exception as e:
            logger.error(f"Error handling user input: {e}")
            message_placeholder.error(f"Error generating response: {str(e)}")


def main():
    """Main application function."""
    # Page configuration
    st.set_page_config(
        page_title=config.page_title,
        page_icon=config.app_icon,
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize session state
    initialize_session_state()

    # Check if knowledge base is empty
    kb_stats = knowledge_base.get_stats()
    is_kb_empty = kb_stats.get('total_documents', 0) == 0

    # Main title
    st.title(f"{config.app_icon} {config.app_title}")

    # Show knowledge base status
    if is_kb_empty:
        st.warning("⚠️ **Knowledge Base is Empty** - Please add documents to start chatting!")
        st.info("""
        This chat application works exclusively with your local knowledge base.
        To start chatting, please add some documents in the **📚 Knowledge Base** tab below.
        """)

    # Create tabs for different sections
    tab1, tab2, tab3 = st.tabs(["💬 Chat", "📚 Knowledge Base", "📋 All Documents"])

    with tab2:
        render_web_content_manager()

    with tab3:
        render_all_documents_manager()

    with tab1:
        if is_kb_empty:
            st.error("🚫 **Chat Disabled - Knowledge Base Required**")
            st.markdown("""
            ### Why is chat disabled?

            This application is designed to work exclusively with your local knowledge base to ensure:
            - **Privacy**: All conversations are based only on your documents
            - **Accuracy**: Responses are grounded in your specific content
            - **Relevance**: No generic AI responses, only information from your sources

            ### To enable chat:
            1. Switch to the **📚 Knowledge Base** tab
            2. Add documents by entering URLs (web pages, PDFs, etc.)
            3. Return here to chat about your documents

            The AI will only answer questions based on the content you've added.
            """)
        else:
            # Render sidebar and get selected model
            selected_model = render_sidebar()

            if not selected_model:
                st.error("No models available. Please ensure Ollama is running and models are installed.")
                st.stop()

            # Handle model change
            if not handle_model_change(selected_model):
                st.stop()

            # Handle session controls
            handle_session_controls()

            # Get current session
            current_session = chat_handler.get_current_session()

            # Main chat area
            if current_session:
                # Show knowledge base info
                st.info(f"💡 **Knowledge Base Active**: {kb_stats.get('total_documents', 0)} documents, {kb_stats.get('total_chunks', 0)} chunks loaded")

                # Session statistics
                render_session_stats(current_session)

                # Chat messages
                render_chat_messages(current_session)

                # Chat input
                user_input = render_chat_input()

                if user_input:
                    handle_user_input(user_input)

                # Show sources from last response
                render_chat_sources_section()
            else:
                st.info("Please select a model to start chatting!")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Application error: {e}")
        st.error(f"Application error: {str(e)}")
        st.error("Please check the logs for more details.")
